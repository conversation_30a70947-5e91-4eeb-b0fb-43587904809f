# 部署VitePress文档到GitHub Pages (完整版)
name: Deploy VitePress to GitHub Pages (Full)

on:
  # 当推送到main分支时触发
  push:
    branches: [ main, master ]

  # 允许手动触发工作流
  workflow_dispatch:

# 设置GITHUB_TOKEN的权限以允许部署到GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 只允许一个并发部署，跳过正在运行和最新队列之间的运行队列
# 但是，不要取消正在进行的运行，因为我们希望让这些生产部署完成
concurrency:
  group: pages
  cancel-in-progress: false

jobs:
  # 构建作业
  build:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 如果未启用lastUpdated，则不需要

      - name: 安装pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: pnpm
      
      - name: 设置Pages
        uses: actions/configure-pages@v4
      
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
      
      - name: 构建VitePress
        run: |
          pnpm run build
          touch .vitepress/dist/.nojekyll
      
      - name: 上传构建产物
        uses: actions/upload-pages-artifact@v3
        with:
          path: .vitepress/dist

  # 部署作业
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    needs: build
    runs-on: ubuntu-latest
    name: 部署
    steps:
      - name: 部署到GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
