---
layout: home

hero:
  name: "股票指标文档库"
  text: "专业的技术指标分析"
  tagline: 深入解析股票技术指标的计算逻辑与应用策略
  actions:
    - theme: brand
      text: 指标文档
      link: /docs/指标/
    - theme: alt
      text: 策略文档
      link: /docs/策略/

features:
  - title: 擒龙启爆指标
    details: 综合性股票技术分析指标，识别股票启动点和爆发时机，结合价格动量、成交量分析和趋势判断
    link: /docs/指标/擒龙启爆/README
  - title: 金牛暴起指标
    details: 专注于识别主力资金进场和强势突破形态，通过开收盘价差异分析捕捉投资机会
    link: /docs/指标/金牛暴起/README
  - title: 高抛低吸指标
    details: 动态区间相对位置模型，帮助投资者把握买卖时机，实现高抛低吸的交易策略
    link: /docs/指标/高抛低吸/README
  - title: 策略实现
    details: 提供完整的策略实现代码，包括掘金版和通用版，支持多种数据源和交易平台
    link: /docs/策略/
  - title: 工作流文档
    details: 自动化工作流程和工具，提高开发效率和文档维护质量
    link: /workflow/文档生成工作流
  - title: 指标总览
    details: 所有指标的概览和快速导航，帮助您快速找到需要的技术指标
    link: /docs/指标/
---

## 快速开始

本文档库包含了三个核心股票技术指标的详细分析和策略实现：

### 🚀 擒龙启爆指标

- **起爆点算法**：多层次确认机制，识别底部启动信号
- **启爆器算法**：量价同步突破模型，捕捉强势爆发
- **应用场景**：适合中短线操作，捕捉启动机会

### 🐂 金牛暴起指标  

- **主力进场算法**：开收盘价差异分析，识别资金流向
- **金牛暴起算法**：强势形态识别，确保信号质量
- **应用场景**：识别主力资金动向，把握上涨趋势

### 📈 高抛低吸指标

- **秘籍线算法**：动态区间相对位置计算
- **探秘线算法**：加权平滑处理，稳定信号
- **逃顶算法**：多重平滑顶部识别
- **应用场景**：波段操作，高抛低吸策略

### 💼 策略实现

- **掘金版策略**：适配掘金量化平台的完整策略代码
- **通用版策略**：支持多种数据源的通用化实现
- **示例代码**：包含akshare、聚宽等平台的示例实现

## 特色功能

- **📊 算法详解**：每个指标都有详细的数学公式和计算步骤
- **🔍 信号分析**：深入分析每个信号的触发条件和实际意义  
- **💻 源码提供**：完整的Python实现代码
- **🎯 选股策略**：基于指标的实用选股公式
- **📚 使用指南**：详细的使用建议和注意事项
- **⚡ 策略实现**：可直接运行的量化交易策略

## 开始探索

选择您感兴趣的内容开始学习：

- [指标文档](/docs/指标/) - 了解各种技术指标的核心原理
- [策略文档](/docs/策略/) - 查看完整的量化交易策略
- [工作流文档](/workflow/文档生成工作流) - 了解自动化工作流程
