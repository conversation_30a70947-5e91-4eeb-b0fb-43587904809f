# 金牛暴起指标源码详细解析

## 指标概述

金牛暴起指标是一个综合性的股票技术分析指标，专门用于识别主力资金进场、股票底部反转和强势启动的关键时机。该指标集成了多种技术分析方法，包括资金流向分析、趋势判断、超买超卖识别等，为投资者提供全方位的交易信号。

## 参数设置

- **时间范围**：SJKS:=DATE>=1220816; SJJS:=DATE<=1250801; (可调整)
- **主要周期**：使用多个时间周期，包括5日、13日、25日、34日、75日等
- **过滤参数**：内置多种过滤机制，避免假信号干扰

## 主要输出信号

### 1. 主力进场信号 (GUB8)

**详细计算逻辑：**

**第一步：开收盘价差异分析**

```
GUB6:=100-3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
GUB7:=100-3*SMA((OPEN-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(SMA((OPEN-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
```

- 分别计算收盘价和开盘价在75日价格区间中的相对位置
- 通过复杂的加权平滑算法处理，识别价格行为差异

**第二步：主力进场确认**

```
GUB8:=GUB6<REF(GUB7,1) AND VOL>REF(VOL,1) AND CLOSE>REF(CLOSE,1);
```

- 收盘价指标低于前一日开盘价指标：表明收盘强于开盘
- 成交量放大：VOL>REF(VOL,1)
- 价格上涨：CLOSE>REF(CLOSE,1)
- 30日内首次出现：COUNT(GUB8,30)=1

**显示效果：**

- 蓝色、橙色、黄色渐变柱状线
- 显示"主力进场★"文字提示

**意义：** 识别主力资金开始建仓的关键时刻，是重要的买入信号。

### 2. 金牛暴起信号 (金牛)

**详细计算逻辑：**

**第一步：涨幅和形态判断**

```
GUP3AA:=IF((CLOSE>REF(CLOSE,1)),88,0);
GUP4AA:=IF(((CLOSE)/(REF(CLOSE,1))>1.05) AND ((HIGH)/(CLOSE)<1.01) AND (GUP3AA>0),91,0);
```

- 当日收盘价高于前一日收盘价
- 当日涨幅超过5%：(CLOSE)/(REF(CLOSE,1))>1.05
- 最高价接近收盘价：(HIGH)/(CLOSE)<1.01，表明收盘强势
- 45日内过滤重复信号：FILTER((GUP4AA>90),45)

**显示效果：**

- 多层次蓝色渐变柱状线
- 显示"金牛暴起"文字标识

**意义：** 识别股票强势突破并保持强势收盘的信号，表明上涨动力强劲。

### 3. 妖底确定信号 (妖底确定)

**详细计算逻辑：**

**第一步：价格压制条件**

```
C1:=((MA(C,30)-L)/MA(C,60))*200;
TU:=C/MA(C,40)<0.74;
```

- C1：30日均线与最低价的关系，反映价格相对位置
- TU：收盘价低于40日均线的74%，确保处于低位

**第二步：动量突破分析**

```
M2:=SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100;
G1:=FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5);
```

- M2：类似RSI的动量指标
- G1：动量从低位开始上升的过滤信号

**第三步：技术形态确认**

```
SMMA:=EMA(EMA(C,5),5);
IM:=EMA(C,5)- REF(EMA(C,5),1);
TSMMA:=SMMA - REF(SMMA,1);
DIVMA:= ABS(EMA(C,5)- SMMA);
TDJ:=(H-L)/REF(C,1)>0.05;
ET:=(IM+TSMMA)/2;
TDF:= POW(DIVMA,1)*POW(ET,3);
NTDF:=TDF/HHV(ABS(TDF),5*3);
YUL:=COUNT(TDJ,5)>1;
启动:=TU AND TDJ AND YUL;
确定:=CROSS(NTDF,-0.9);
```

- 通过复杂的数学模型计算技术形态
- 结合价格波动、趋势变化等多个因素

**第四步：最终确认机制**

```
波段:=FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(启动,1),10);
选股:=FILTER(REF(启动,1) AND (确定 OR C>REF(C,1)) AND MACD.MACD>-1.5,10);
妖底确定:(COUNT(选股,13)>=1 AND 波段)*10;
```

**显示效果：**

- 红色线条信号
- 显示"妖底确定"文字标识

**意义：** 识别股票底部反转的确定性信号，是高质量的买入机会。

### 4. 短线出击信号 (短线出击)

**详细计算逻辑：**

**第一步：短线动量计算**

```
BBB:=SMA(MAX(CLOSE-REF(C,1),0),5,1)/SMA(ABS(CLOSE-REF(C,1)),5,1)*1000;
HHH:=BBB-LLV(BBB,10);
SS:=(MA(HHH,2)*3+HHH*13)/16;
短线买点:=IF(SS>13,MA(SS,2),SS)/6;
```

- BBB：放大的动量指标
- HHH：相对于10日最低值的位置
- SS：加权平滑处理
- 短线买点：最终的短线信号强度

**第二步：MACD确认**

```
指标:=MACD.DIF>MACD.DEA AND MACD.DIF<0.2 AND MACD.DIF>0;
短线出击:IF(CROSS(短线买点,1) AND (短线买点<30) AND 指标 AND SJTJ,20,0);
```

- MACD金叉且DIF在合理区间
- 短线买点突破1且小于30
- 时间条件满足

**显示效果：**

- 显示"短线出击"文字提示

**意义：** 识别短线交易的最佳买入时机，适合短线操作。

### 5. 妖股信号 (妖股)

**详细计算逻辑：**

**第一步：游资线计算**

```
AK1:=ABS(((3.48*CLOSE+HIGH+LOW)/4-EMA(CLOSE,23))/EMA(CLOSE,23));
AK2:=DMA(((2.15*CLOSE+LOW+HIGH)/4),AK1);
游资:=EMA(AK2,200)*1.1;
```

- 通过复杂的价格加权算法计算游资关注线

**第二步：妖股确认条件**

```
VA2:=V>MA(V,89);  // 成交量放大
VA3:=EXPMA(C,5);
VA4:=EXPMA(C,29);
VA5:=VA3>VA4;     // 短期均线上穿长期均线
RSI1:=SMA(MAX(CLOSE-LC,0),12,1)/SMA(ABS(CLOSE-LC),12,1)*100;
RSI2:=SMA(MAX(CLOSE-LC,0),56,1)/SMA(ABS(CLOSE-LC),56,1)*100;
VA6:=RSI1>RSI2 AND VA5 AND VA2;
VASS3:=REF((VASS1/VASS2-1)*100<=30,1);  // 前日振幅条件
妖股:CROSS(C,游资) AND REF(C*1.097,1)<C AND VA6 AND VASS3;
```

**显示效果：**

- 洋红色粗线信号
- 显示"妖股"文字标识

**意义：** 识别具有妖股特征的强势股票，适合激进投资者。

### 6. 黑马启动信号 (黑马启动)

**详细计算逻辑：**

**第一步：金线王计算**

```
FA1:=ABS(((3.48*CLOSE+HIGH+LOW)/4-EMA(CLOSE,23))/EMA(CLOSE,23));
FA2:=DMA(((2.15*CLOSE+LOW+HIGH)/4),FA1);
金线王:=EMA(FA2,200)*1.118;
```

**第二步：启动条件**

```
条件:=(C-REF(C,1))/REF(C,1)*100>8;  // 当日涨幅超过8%
金K线:=CROSS(C,金线王) AND 条件;
```

**显示效果：**

- 红色粗线信号
- 显示"黑马启动"文字标识
- 图标标识

**意义：** 识别黑马股的启动时刻，通常伴随大幅上涨。

### 7. 趋势指示系统

**动力线和趋势线**

```
VAR2A:=LLV(LOW,10);
VAR3A:=HHV(HIGH,25);
动力线:= EMA((CLOSE-VAR2A)/(VAR3A-VAR2A)*4,4);
趋势:MA(动力线,2)*30;
```

**显示效果：**

- 红色：趋势向上
- 蓝色：趋势向下

**意义：** 显示股票的整体趋势方向，辅助判断大方向。

## 使用建议

### 信号优先级排序

1. **妖底确定**：最高优先级，底部反转确定信号
2. **主力进场**：高优先级，资金流入信号
3. **金牛暴起**：中高优先级，强势突破信号
4. **黑马启动**：中等优先级，大涨潜力信号
5. **妖股信号**：激进信号，适合风险偏好高的投资者
6. **短线出击**：短线交易信号

### 组合使用策略

- **最佳买入组合**：妖底确定 + 主力进场
- **强势追涨组合**：金牛暴起 + 趋势向上
- **短线操作组合**：短线出击 + MACD金叉
- **激进操作组合**：妖股 + 黑马启动

### 风险控制建议

1. **仓位控制**：单只股票不超过总资金的20%
2. **止损设置**：设置8-10%的止损位
3. **分批操作**：可以分批建仓和减仓
4. **趋势确认**：结合趋势线确认大方向

## 注意事项

1. **时间有效性**：注意指标的时间范围设置
2. **市场环境**：在极端市场环境下谨慎使用
3. **成交量确认**：重要信号需要成交量配合
4. **基本面结合**：建议结合基本面分析
5. **风险意识**：任何技术指标都不能保证100%准确

## 技术特点

- **多维度分析**：整合资金流、技术形态、趋势等多个维度
- **智能过滤**：内置多种过滤机制，减少假信号
- **层次化设计**：从保守到激进的多层次信号体系
- **视觉化显示**：丰富的颜色和文字提示，便于识别
- **适应性强**：适用于不同市场环境和投资风格
