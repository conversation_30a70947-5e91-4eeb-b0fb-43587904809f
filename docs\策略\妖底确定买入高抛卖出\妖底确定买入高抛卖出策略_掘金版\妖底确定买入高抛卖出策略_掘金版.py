# coding=utf-8
"""
妖底确定买入高抛卖出量化策略 - 掘金平台版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前5分钟(14:55)使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位，已持有股票可继续加仓
3. 收盘前5分钟检测持仓股票是否触发卖出信号
4. 如有卖出信号则全仓卖出

适配掘金平台特点：
- 使用掘金API获取数据和执行交易
- 保持原有策略逻辑不变
- 支持回测和实盘交易
"""

from __future__ import print_function, absolute_import, unicode_literals
from gm.api import *
import pandas as pd
import numpy as np
import datetime
import random

# 尝试导入talib，如果不可用则使用备用方案
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("talib不可用，将使用备用MACD计算方法")


def init(context):
    """
    策略初始化函数
    """
    # 策略配置
    context.position_ratio = 0.1  # 单次买入仓位比例（十分之一）
    context.trade_time = "14:55"  # 交易时间
    context.commission_rate = 0.0003  # 手续费率
    context.stamp_tax = 0.001  # 印花税
    context.min_commission = 5  # 最小手续费
    
    # 每日定时任务 - 收盘前3分钟执行策略
    schedule(schedule_func=trade_strategy, date_rule='1d', time_rule='14:57:00')

    # 收盘后回测验证任务
    schedule(schedule_func=after_market_backtest, date_rule='1d', time_rule='15:30:00')

    # 初始化当日买入信号记录
    context.daily_buy_signals = []

    print('妖底确定买入高抛卖出策略初始化完成')
    print(f'策略参数: 单次买入仓位比例={context.position_ratio}')


def trade_strategy(context):
    """
    主交易策略函数
    """
    try:
        print(f"\n=== {context.now} 策略执行开始 ===")
        
        # 1. 优先检查卖出信号
        check_sell_signals(context)
        
        # 2. 检查买入信号
        check_buy_signals(context)
        
        # 3. 记录当前持仓状态
        log_positions(context)
        
        print("=== 策略执行完成 ===\n")
        
    except Exception as e:
        print(f"策略执行出错: {e}")



def sma_tdx(series, period, weight=1):
    """
    通达信SMA函数的精确实现
    SMA(X, N, M) = (M*X + (N-M)*SMA')/N
    其中SMA'是前一个SMA值
    """
    return series.ewm(alpha=weight/period, adjust=False).mean()


def filter_tdx(condition, period):
    """
    通达信FILTER函数的精确实现
    FILTER(X, N): 当X条件成立时，将其后N周期内的数据置为0
    这里用rolling sum > 0来近似，但可能存在差异
    """
    return condition.rolling(period).sum() > 0


def cross_tdx(series1, series2):
    """
    通达信CROSS函数的精确实现
    CROSS(A, B): A向上穿越B时返回1
    支持Series与Series、Series与数值的交叉
    """
    if isinstance(series2, (int, float)):
        # 数值与Series的交叉
        return (series1 > series2) & (series1.shift(1) <= series2)
    else:
        # Series与Series的交叉
        return (series1 > series2) & (series1.shift(1) <= series2.shift(1))


def calculate_yaodi_signal_with_details(context, stock):
    """
    计算妖底确定买入信号并返回详细指标信息
    用于收盘后回测验证和对比分析
    """
    try:
        # 获取足够的历史数据
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=200)).strftime('%Y-%m-%d')

        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is None or len(df) < 100:
            return False, {}

        # 重命名列以匹配公式
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})

        C = df['C']
        H = df['H']
        L = df['L']

        # --- 核心指标计算 (与聚宽版对齐) ---

        # C1指标
        MA30 = C.rolling(30).mean()
        MA60 = C.rolling(60).mean()
        C1 = ((MA30 - L) / MA60) * 200

        # M2指标 - 使用通达信SMA精确实现
        price_changes = C.diff()
        abs_changes = price_changes.abs()
        positive_changes = price_changes.where(price_changes > 0, 0)
        M2 = sma_tdx(positive_changes, 7, 1) / sma_tdx(abs_changes, 7, 1) * 100

        # G1条件 - 使用通达信FILTER精确实现
        g1_base = (M2.shift(1) < 20) & (M2 > M2.shift(1))
        G1 = filter_tdx(g1_base, 5)

        # TU条件：超跌状态
        MA40 = C.rolling(40).mean()
        TU = C / MA40 < 0.74

        # TDJ条件：振幅大于5%
        TDJ = (H - L) / C.shift(1) > 0.05

        # YUL条件: 5日内TDJ次数>1
        YUL = TDJ.rolling(5).sum() > 1

        # QD启动条件
        QD = TU & TDJ & YUL

        # NTDF相关计算
        EMA5 = C.ewm(span=5, adjust=False).mean()
        SMMA = EMA5.ewm(span=5, adjust=False).mean()
        IM = EMA5.diff()
        TSMMA = SMMA.diff()
        DIVMA = (EMA5 - SMMA).abs()
        ET = (IM + TSMMA) / 2
        TDF = DIVMA * (ET ** 3)
        NTDF = TDF / TDF.abs().rolling(15).max()

        # QR确定条件 - 使用通达信CROSS精确实现
        QR = cross_tdx(NTDF, -0.9)

        # MACD 计算
        MACD = calculate_macd(C)

        # XG选股条件 - 使用通达信FILTER精确实现
        xg_base = QD.shift(1) & (QR | (C > C.shift(1))) & (MACD > -1.5)
        XG = filter_tdx(xg_base, 10)

        # BD波段条件 - 使用通达信FILTER精确实现
        bd_base = ((G1 & (C1 > 20)) | (C > C.shift(1))) & QD.shift(1)
        BD = filter_tdx(bd_base, 10)

        # 最终妖底确定条件: COUNT(XG,13)>=1 AND BD
        final_cond1 = XG.rolling(13).sum() >= 1
        final_cond2 = BD

        # --- 信号判断 ---
        if len(final_cond1) == 0 or len(final_cond2) == 0:
            return False, {}

        last_signal1 = final_cond1.iloc[-1] if not pd.isna(final_cond1.iloc[-1]) else False
        last_signal2 = final_cond2.iloc[-1] if not pd.isna(final_cond2.iloc[-1]) else False

        yaodi_signal = bool(last_signal1 and last_signal2)

        # 返回详细指标信息
        details = {
            'M2': M2.iloc[-1] if len(M2) > 0 else 0,
            'C1': C1.iloc[-1] if len(C1) > 0 else 0,
            'NTDF': NTDF.iloc[-1] if len(NTDF) > 0 else 0,
            'MACD': MACD.iloc[-1] if len(MACD) > 0 else 0,
            'XG_count': XG.rolling(13).sum().iloc[-1] if len(XG) > 0 else 0,
            'BD': BD.iloc[-1] if len(BD) > 0 else False,
            'TU': TU.iloc[-1] if len(TU) > 0 else False,
            'TDJ': TDJ.iloc[-1] if len(TDJ) > 0 else False,
            'YUL': YUL.iloc[-1] if len(YUL) > 0 else False,
            'QD': QD.iloc[-1] if len(QD) > 0 else False,
            'QR': QR.iloc[-1] if len(QR) > 0 else False,
            'G1': G1.iloc[-1] if len(G1) > 0 else False,
            'final_cond1': last_signal1,
            'final_cond2': last_signal2
        }

        return yaodi_signal, details

    except Exception as e:
        print(f"计算妖底确定信号详情出错 {stock}: {e}")
        return False, {}


def calculate_macd(close_prices, fast=12, slow=26, signal=9):
    """
    计算MACD指标，使用ewm以确保一致性
    """
    dif = close_prices.ewm(span=fast, adjust=False).mean() - close_prices.ewm(span=slow, adjust=False).mean()
    dea = dif.ewm(span=signal, adjust=False).mean()
    macd = (dif - dea) * 2
    return macd


def calculate_yaodi_signal(context, stock):
    """
    计算妖底确定买入信号 - 与通达信版逻辑完全对齐
    """
    try:
        # 获取足够的历史数据
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=200)).strftime('%Y-%m-%d')
        
        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date, 
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True, 
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)
        
        if df is None or len(df) < 100:
            return False
            
        # 重命名列以匹配公式
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})
        
        C = df['C']
        H = df['H']
        L = df['L']
        
        # --- 核心指标计算 (与聚宽版对齐) ---

        # C1指标
        MA30 = C.rolling(30).mean()
        MA60 = C.rolling(60).mean()
        C1 = ((MA30 - L) / MA60) * 200

        # M2指标 - 使用通达信SMA精确实现
        price_changes = C.diff()
        abs_changes = price_changes.abs()
        positive_changes = price_changes.where(price_changes > 0, 0)
        M2 = sma_tdx(positive_changes, 7, 1) / sma_tdx(abs_changes, 7, 1) * 100

        # G1条件 - 使用通达信FILTER精确实现
        g1_base = (M2.shift(1) < 20) & (M2 > M2.shift(1))
        G1 = filter_tdx(g1_base, 5)

        # TU条件：超跌状态
        MA40 = C.rolling(40).mean()
        TU = C / MA40 < 0.74

        # TDJ条件：振幅大于5%
        TDJ = (H - L) / C.shift(1) > 0.05

        # YUL条件: 5日内TDJ次数>1
        YUL = TDJ.rolling(5).sum() > 1

        # QD启动条件
        QD = TU & TDJ & YUL

        # NTDF相关计算
        EMA5 = C.ewm(span=5, adjust=False).mean()
        SMMA = EMA5.ewm(span=5, adjust=False).mean()
        IM = EMA5.diff()
        TSMMA = SMMA.diff()
        DIVMA = (EMA5 - SMMA).abs()
        ET = (IM + TSMMA) / 2
        TDF = DIVMA * (ET ** 3)
        NTDF = TDF / TDF.abs().rolling(15).max()

        # QR确定条件 - 使用通达信CROSS精确实现
        QR = cross_tdx(NTDF, -0.9)

        # MACD 计算
        MACD = calculate_macd(C)

        # XG选股条件 - 使用通达信FILTER精确实现
        xg_base = QD.shift(1) & (QR | (C > C.shift(1))) & (MACD > -1.5)
        XG = filter_tdx(xg_base, 10)

        # BD波段条件 - 使用通达信FILTER精确实现
        bd_base = ((G1 & (C1 > 20)) | (C > C.shift(1))) & QD.shift(1)
        BD = filter_tdx(bd_base, 10)

        # 最终妖底确定条件: COUNT(XG,13)>=1 AND BD
        final_cond1 = XG.rolling(13).sum() >= 1
        final_cond2 = BD

        # --- 信号判断 ---
        if len(final_cond1) == 0 or len(final_cond2) == 0:
            return False

        last_signal1 = final_cond1.iloc[-1] if not pd.isna(final_cond1.iloc[-1]) else False
        last_signal2 = final_cond2.iloc[-1] if not pd.isna(final_cond2.iloc[-1]) else False

        yaodi_signal = bool(last_signal1 and last_signal2)

        if yaodi_signal:
            # 获取股票名称以便打印
            try:
                inst = get_instrumentinfos(symbols=[stock])
                stock_name = inst[0]['sec_name'] if inst else '未知名称'
                print(f"发现买入信号: {stock} ({stock_name})")

                # 调试信息 - 输出关键指标值用于对比
                print(f"  调试信息: M2={M2.iloc[-1]:.4f}, C1={C1.iloc[-1]:.4f}, NTDF={NTDF.iloc[-1]:.4f}")
                print(f"  调试信息: XG_count={XG.rolling(13).sum().iloc[-1]}, BD={BD.iloc[-1]}")

            except Exception:
                print(f"发现买入信号: {stock}")

        return yaodi_signal
        
    except Exception as e:
        print(f"计算妖底确定信号出错 {stock}: {e}")
        return False


def calculate_gaopao_sell_signal(context, stock):
    """
    计算高抛低吸卖出信号 - 与通达信卖出选股指标完全对齐
    """
    try:
        # 获取历史数据
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=150)).strftime('%Y-%m-%d')

        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is None or len(df) < 80:
            return False

        # 重命名列
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})

        C = df['C']
        H = df['H']
        L = df['L']

        # 参数设置
        N1 = 21
        N2 = 8

        # === 完整的通达信卖出选股指标计算 ===

        # VAR1: 3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1)
        llv75 = L.rolling(75).min()
        hhv75 = H.rolling(75).max()
        denominator75 = hhv75 - llv75
        denominator75 = denominator75.where(denominator75 != 0, 1)

        base_ratio = (C - llv75) / denominator75 * 100
        sma20_1 = sma_tdx(base_ratio, 20, 1)
        sma15_1 = sma_tdx(sma20_1, 15, 1)
        VAR1 = 3 * sma20_1 - 2 * sma15_1

        # VAR2: (CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100
        llv26 = L.rolling(26).min()
        hhv26 = H.rolling(26).max()
        denominator26 = hhv26 - llv26
        denominator26 = denominator26.where(denominator26 != 0, 1)
        VAR2 = (C - llv26) / denominator26 * 100

        # VAR3: SMA(SMA(VAR2,3,1),3,1)
        VAR3 = sma_tdx(sma_tdx(VAR2, 3, 1), 3, 1)

        # VAR4: EMA(VAR3,5)
        VAR4 = VAR3.ewm(span=5, adjust=False).mean()

        # VAR5: LLV(LOW,26)
        VAR5 = llv26

        # VAR6: HHV(HIGH,34)
        VAR6 = H.rolling(34).max()

        # VAR7: EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25
        denominator_var7 = VAR6 - VAR5
        denominator_var7 = denominator_var7.where(denominator_var7 != 0, 1)
        VAR7 = ((C - VAR5) / denominator_var7 * 4).ewm(span=4, adjust=False).mean() * 25

        # VAR8: (2*C+H+L)/4
        VAR8 = (2 * C + H + L) / 4

        # VAR9: LLV(LOW,N1)
        VAR9 = L.rolling(N1).min()

        # VAR10: HHV(HIGH,N2)
        VAR10 = H.rolling(N2).max()

        # VAR2W: 100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14))
        hhv14 = H.rolling(14).max()
        llv14 = L.rolling(14).min()
        denominator14 = hhv14 - llv14
        denominator14 = denominator14.where(denominator14 != 0, 1)
        VAR2W = 100 - 100 * (hhv14 - C) / denominator14

        # MW: EMA(VAR2W,3)
        MW = VAR2W.ewm(span=3, adjust=False).mean()

        # VAR3W: EMA(VAR2W,7)
        VAR3W = VAR2W.ewm(span=7, adjust=False).mean()

        # M1: EMA(VAR3W,5)
        M1 = VAR3W.ewm(span=5, adjust=False).mean()

        # MB1: CROSS(MW,M1) AND M1<20
        MB1 = cross_tdx(MW, M1) & (M1 < 20)

        # MG1: IF(CROSS(M1,MW) AND REF(MW,1)>80,80,0)
        cross_condition = cross_tdx(M1, MW) & (MW.shift(1) > 80)
        MG1 = cross_condition.astype(int) * 80

        # 核心指标计算
        # MJ: EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9)
        denominator_mj = VAR10 - VAR9
        denominator_mj = denominator_mj.where(denominator_mj != 0, 1)
        MJ = ((VAR8 - VAR9) / denominator_mj * 100).ewm(span=9, adjust=False).mean()

        # TM: EMA(0.667*REF(MJ,1)+0.333*MJ,2)
        TM = (0.667 * MJ.shift(1) + 0.333 * MJ).ewm(span=2, adjust=False).mean()

        # 卖出条件：CROSS(80,MJ) - 80上穿MJ，即MJ从上方跌破80
        # 这里需要反向理解：80上穿MJ意味着MJ从上方跌破80
        sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)

        if len(sell_signal) > 0 and sell_signal.iloc[-1]:
            print(f"发现卖出信号: {stock} (MJ={MJ.iloc[-1]:.2f}, TM={TM.iloc[-1]:.2f})")
            return True
        else:
            return False

    except Exception as e:
        print(f"计算高抛低吸卖出信号出错 {stock}: {e}")
        return False


def get_stock_pool(context):
    """
    获取股票池，排除不符合条件的股票
    """
    try:
        # 获取所有A股股票
        stocks = get_instruments(exchanges=['SHSE', 'SZSE'], sec_types=[SEC_TYPE_STOCK])
        
        stock_list = []
        for stock in stocks:
            symbol = stock['symbol']
            
            # 排除北证股票（代码以4或8开头）
            if symbol.startswith('4') or symbol.startswith('8'):
                continue
                
            # 排除ST股票
            if 'ST' in stock.get('sec_name', ''):
                continue
                
            stock_list.append(symbol)
        
        return stock_list
        
    except Exception as e:
        print(f"获取股票池出错: {e}")
        return []


def check_buy_signals(context):
    """
    检查买入信号
    """
    try:
        # 清空当日买入信号记录
        context.daily_buy_signals = []

        # 获取股票池
        stock_pool = get_stock_pool(context)

        if not stock_pool:
            print("股票池为空，跳过买入检查")
            return
        
        # 获取当前账户信息
        account = context.account()
        available_cash = account.cash['available']  # 正确的可用资金
        
        if available_cash < 10000:  # 可用资金少于1万元时不买入
            print(f"可用资金不足: {available_cash:.2f}")
            return
        
        # 计算单次买入金额
        buy_amount = available_cash * context.position_ratio
        
        print(f"开始全市场扫描买入信号，股票池大小: {len(stock_pool)}...")
        
        buy_signals = []
        # 遍历整个股票池来寻找买入信号
        for i, stock in enumerate(stock_pool):
            # 打印进度
            if (i + 1) % 100 == 0:
                print(f"  已扫描 {i + 1}/{len(stock_pool)}...")

            try:
                if calculate_yaodi_signal(context, stock):
                    buy_signals.append(stock)
                    # 记录当日买入信号用于收盘后回测
                    context.daily_buy_signals.append(stock)
                    # 注意：这里不再提前停止

            except Exception as e:
                print(f"检查股票 {stock} 买入信号出错: {e}")
                continue
        
        print(f"全市场扫描完成，共找到 {len(buy_signals)} 个买入信号。")

        # 一次性获取所有待买入股票的名称，提高效率
        buy_name_map = {}
        if buy_signals:
            try:
                buy_instruments = get_instrumentinfos(symbols=buy_signals)
                buy_name_map = {inst['symbol']: inst['sec_name'] for inst in buy_instruments}
            except Exception as e:
                print(f"获取待买入股票名称时出错: {e}")

        # 执行买入 - 买入所有扫描到的信号
        for stock in buy_signals:
            try:
                # 获取当前价格
                current_data = current(symbols=[stock])
                # 使用我们通过调试发现的正确键名 'price'
                if not current_data or current_data[0]['price'] <= 0:
                    print(f"无法获取 {stock} 当前价格或价格无效（可能停牌），跳过买入")
                    continue
                
                current_price = current_data[0]['price']
                shares = int(buy_amount / current_price / 100) * 100  # 按手买入
                
                if shares >= 100:
                    order_target_value(symbol=stock, 
                                       value=shares * current_price, 
                                       position_side=PositionSide_Long, 
                                       order_type=OrderType_Market)
                    
                    stock_name = buy_name_map.get(stock, '')
                    print(f"买入 {stock} ({stock_name}): {shares}股, 价格: {current_price:.2f}")
                else:
                    print(f"{stock} 计算股数不足100股，跳过买入")
                    
            except Exception as e:
                print(f"买入 {stock} 出错: {e}")
                
    except Exception as e:
        print(f"检查买入信号出错: {e}")


def check_sell_signals(context):
    """
    检查卖出信号
    """
    try:
        # 获取当前持仓
        positions = context.account().positions()
        
        if not positions:
            print("当前无持仓，跳过卖出检查")
            return
        
        print(f"开始检查卖出信号，持仓股票数: {len(positions)}")
        
        for position in positions:
            stock = position['symbol']
            
            try:
                if calculate_gaopao_sell_signal(context, stock):
                    # 全仓卖出
                    order_target_percent(symbol=stock,
                                       percent=0,
                                       position_side=PositionSide_Long,
                                       order_type=OrderType_Market)
                    print(f"卖出 {stock}: 全仓卖出")

            except Exception as e:
                print(f"检查股票 {stock} 卖出信号出错: {e}")
                
    except Exception as e:
        print(f"检查卖出信号出错: {e}")


def after_market_backtest(context):
    """
    收盘后对当天买入信号股票进行回测验证
    """
    try:
        print(f"\n=== {context.now} 收盘后回测验证开始 ===")

        if not hasattr(context, 'daily_buy_signals') or not context.daily_buy_signals:
            print("当日无买入信号，跳过回测验证")
            return

        print(f"当日买入信号股票数量: {len(context.daily_buy_signals)}")

        # 获取股票名称信息
        try:
            instruments = get_instrumentinfos(symbols=context.daily_buy_signals)
            name_map = {inst['symbol']: inst['sec_name'] for inst in instruments}
        except Exception as e:
            print(f"获取股票名称出错: {e}")
            name_map = {}

        # 对每只买入信号股票进行收盘后验证
        verified_signals = []
        failed_signals = []

        for stock in context.daily_buy_signals:
            try:
                # 使用收盘后的完整数据重新验证信号，获取详细指标
                signal_result, details = calculate_yaodi_signal_with_details(context, stock)
                stock_name = name_map.get(stock, '未知名称')

                if signal_result:
                    verified_signals.append(stock)
                    print(f"✓ 收盘后验证通过: {stock} ({stock_name})")
                    print(f"    指标详情: M2={details.get('M2', 0):.4f}, C1={details.get('C1', 0):.4f}, "
                          f"NTDF={details.get('NTDF', 0):.4f}, XG_count={details.get('XG_count', 0)}")
                else:
                    failed_signals.append((stock, details))
                    print(f"✗ 收盘后验证失败: {stock} ({stock_name})")
                    print(f"    失败原因分析:")
                    print(f"      XG条件: {details.get('XG_count', 0)} >= 1? {details.get('final_cond1', False)}")
                    print(f"      BD条件: {details.get('BD', False)}")
                    print(f"      关键指标: M2={details.get('M2', 0):.4f}, C1={details.get('C1', 0):.4f}")

            except Exception as e:
                print(f"验证股票 {stock} 出错: {e}")

        # 统计验证结果
        total_signals = len(context.daily_buy_signals)
        verified_count = len(verified_signals)
        failed_count = len(failed_signals)
        verification_rate = (verified_count / total_signals * 100) if total_signals > 0 else 0

        print(f"\n回测验证结果:")
        print(f"盘中信号数量: {total_signals}")
        print(f"收盘后验证通过: {verified_count}")
        print(f"收盘后验证失败: {failed_count}")
        print(f"验证通过率: {verification_rate:.1f}%")

        # 分析失败原因
        if failed_signals:
            print(f"\n失败信号分析:")
            xg_failures = sum(1 for _, details in failed_signals if not details.get('final_cond1', False))
            bd_failures = sum(1 for _, details in failed_signals if not details.get('BD', False))
            print(f"  XG条件失败: {xg_failures} 个")
            print(f"  BD条件失败: {bd_failures} 个")

        if verification_rate < 80:
            print("⚠️  验证通过率较低，建议检查信号计算逻辑")
        elif verification_rate >= 95:
            print("✅ 验证通过率很高，信号质量良好")
        else:
            print("ℹ️  验证通过率正常")

        print("=== 收盘后回测验证完成 ===\n")

    except Exception as e:
        print(f"收盘后回测验证出错: {e}")


def log_positions(context):
    """
    记录当前持仓状态
    """
    try:
        account = context.account()
        positions = account.positions()

        print(f"\n当前账户状态:")
        print(f"总资产: {account.cash['nav']:.2f}")
        print(f"可用资金: {account.cash['available']:.2f}")
        print(f"浮动盈亏: {account.cash['fpnl']:.2f}")
        print(f"持仓数量: {len(positions)}")

        if not positions:
            return

        print("持仓详情:")

        # 一次性获取所有持仓股票的附加信息
        symbols = [p.symbol for p in positions]
        instruments = get_instrumentinfos(symbols=symbols)
        daily_data = current(symbols=symbols)

        # 创建方便查询的字典
        name_map = {inst['symbol']: inst['sec_name'] for inst in instruments}
        daily_map = {bar['symbol']: bar for bar in daily_data}

        for position in positions:
            symbol = position.symbol
            stock_name = name_map.get(symbol, '未知名称')

            # 获取浮动盈亏
            floating_pnl = position.fpnl

            # 计算当日涨跌幅
            daily_change_pct = 0.0
            daily_info = daily_map.get(symbol)
            if daily_info and daily_info.get('price', 0) > 0:
                price = daily_info['price']
                # 在回测模式下，current()函数不返回pre_close字段，跳过涨跌幅计算
                pre_close = daily_info.get('pre_close', 0)
                if pre_close > 0:
                    daily_change_pct = (price - pre_close) / pre_close * 100

            print(f"  代码: {symbol} ({stock_name}), "
                  f"数量: {position.volume}股, "
                  f"浮动盈动: {floating_pnl:.2f}, "
                  f"当日涨跌幅: {daily_change_pct:.2f}%")

    except Exception as e:
        print(f"记录持仓状态出错: {e}")


if __name__ == '__main__':
    """
    策略运行入口
    """
    run(strategy_id='yaodi_gaopao_strategy',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='{{token}}',
        backtest_start_time='2025-01-01 08:00:00',
        backtest_end_time='2025-07-01 16:00:00',
        backtest_initial_cash=1000000,
        backtest_commission_ratio=0.0003,
        backtest_slippage_ratio=0.0001)
