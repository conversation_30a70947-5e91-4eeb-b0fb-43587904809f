# 文档生成工作流

## 工作流概述
本文档描述了为股票技术分析指标源码和量化策略生成详细文档的完整工作流程，包括源码分析、逻辑解析、文档编写、文件结构组织和VitePress配置更新等环节。

## 工作流程图
```
源码分析 → 输出识别 → 逻辑解析 → 文档编写 → 文件结构组织 → VitePress配置更新 → 质量检查 → 总结归档
```

## 详细工作步骤

### 第一阶段：源码分析与准备

#### 1.1 文件结构扫描
- **目标**：识别所有指标文件夹和源码文件
- **工具**：`view` 工具扫描目录结构
- **输出**：完整的文件清单

**操作示例：**
```bash
# 扫描根目录
view . directory
# 识别各指标文件夹：擒龙启爆、金牛暴起、高抛低吸等
```

#### 1.2 源码文件读取
- **目标**：获取每个指标的完整源码
- **范围**：指标源码.py、选股公式.py等
- **方法**：逐个读取并分析代码结构

**操作示例：**
```bash
# 读取指标源码
view 擒龙启爆/指标源码.py file
view 擒龙启爆/起爆点选股.py file
```

### 第二阶段：输出信号识别

#### 2.1 信号类型分类
- **显示信号**：STICKLINE、DRAWTEXT、DRAWICON等
- **计算信号**：各种变量和条件判断
- **选股信号**：FILTER、CROSS等触发条件

#### 2.2 信号提取方法
- **文本标识**：提取DRAWTEXT中的文字内容
- **颜色标识**：识别COLOR相关的显示效果
- **条件逻辑**：分析IF、AND、OR等逻辑条件

**提取规则：**
```
DRAWTEXT(条件, 位置, '文字') → 文字提示信号
STICKLINE(条件, 起点, 终点, 宽度, 类型) → 柱状线信号
变量名:=计算公式 → 计算指标信号
```

### 第三阶段：计算逻辑解析

#### 3.1 逻辑分层分析
- **第一层**：基础数据计算（价格、成交量等）
- **第二层**：技术指标计算（移动平均、相对位置等）
- **第三层**：信号生成逻辑（交叉、突破等）
- **第四层**：过滤和确认机制（FILTER、时间窗口等）

#### 3.2 数学公式解构
- **分步骤拆解**：将复杂公式分解为多个简单步骤
- **参数说明**：解释每个参数的含义和作用
- **计算原理**：阐述数学模型的理论基础

**解析模板：**
```
**第X步：步骤名称**
```
计算公式
```
- 参数1说明
- 参数2说明
- 整体逻辑说明
```

#### 3.3 市场意义解释
- **技术含义**：从技术分析角度解释指标意义
- **交易含义**：说明信号的买卖指导意义
- **风险提示**：指出使用时的注意事项

### 第四阶段：README文档编写

#### 4.1 文档结构设计
```markdown
# 指标名称说明

## 指标概述
## 参数设置（如有）
## 主要输出信号
### 1. 信号名称1
### 2. 信号名称2
...
## 选股公式说明（如有）
## 使用建议
## 注意事项
```

#### 4.2 内容编写规范

**信号描述格式：**
```markdown
### X. 信号名称 (变量名)
**详细计算逻辑：**

**第一步：步骤名称**
```
代码块
```
- 详细说明

**显示效果：**
- 视觉表现描述

**意义：** 实际含义解释
```

#### 4.3 质量控制要点
- **完整性**：确保所有输出信号都有说明
- **准确性**：计算逻辑与源码完全一致
- **可读性**：使用清晰的语言和结构
- **实用性**：提供具体的使用建议

### 第五阶段：文件结构组织

#### 5.1 创建独立文件夹
- **目标**：为每个Python文件创建同名文件夹
- **结构**：`docs/指标/[指标名]/[文件名]/`
- **内容**：index.md + 对应的.py文件
- **重要**：必须移动文件而不是复制，避免重复文件
- **文件命名规则**：使用 index.md 而不是 README.md，以符合 VitePress 默认约定

**操作步骤：**
```bash
# 为每个Python文件创建文件夹
mkdir -p "docs/指标/擒龙启爆/指标源码"
mkdir -p "docs/指标/擒龙启爆/起爆点选股"
# 移动文档和源码到对应文件夹
mv "指标源码.md" "docs/指标/擒龙启爆/指标源码/index.md"
mv "指标源码.py" "docs/指标/擒龙启爆/指标源码/"
```

**文件命名重要说明：**
- **VitePress 约定**：VitePress 默认查找 index.md 作为目录的默认页面
- **避免重复生成**：如果已有 README.md 文件，应重命名为 index.md，而不是重复创建
- **统一性原则**：所有目录都使用 index.md 确保导航一致性

#### 5.2 创建导航索引
- **目标**：为每个主要目录创建index.md文件
- **内容**：系统概述、组成说明、使用建议
- **链接**：指向各个子文件夹的index.md（VitePress会自动处理）

**index.md结构：**
```markdown
# [指标/策略]系统名称

## 概述
## 系统组成
### 指标源码
- **文件位置**: [链接](./文件夹名/) - VitePress自动指向index.md
- **Python文件**: [链接](./文件夹名/文件名.py)
- **功能说明**: 简要描述

## 主要特点
## 使用建议
## 注意事项
```

### 第六阶段：VitePress配置更新

#### 6.1 更新主导航配置
- **文件**：`.vitepress/config.mjs`
- **目标**：更新主导航栏链接
- **重点**：更新工作流文档链接

**主导航更新：**
```javascript
nav: [
  { text: '首页', link: '/' },
  { text: '指标', link: '/docs/指标/' },
  { text: '策略', link: '/docs/策略/' },
  { text: '工作流', link: '/workflow/文档生成工作流' }  // 更新文件名
]
```

#### 6.2 更新指标侧边栏配置
- **目标**：为所有指标文档创建完整的导航结构
- **层次**：总览 → 系统概述 → 详细解析

**指标侧边栏结构：**
```javascript
'/docs/指标/': [
  {
    text: '指标总览',
    items: [
      { text: '指标概述', link: '/docs/指标/' }
    ]
  },
  {
    text: '指标详解',
    items: [
      { text: '擒龙启爆指标系统', link: '/docs/指标/擒龙启爆/' },
      { text: '金牛暴起指标系统', link: '/docs/指标/金牛暴起/' },
      { text: '高抛低吸指标系统', link: '/docs/指标/高抛低吸/' }
    ]
  },
  {
    text: '擒龙启爆指标',
    items: [
      { text: '指标源码解析', link: '/docs/指标/擒龙启爆/指标源码/' },
      { text: '起爆点选股解析', link: '/docs/指标/擒龙启爆/起爆点选股/' }
    ]
  },
  // ... 其他指标配置
]
```

#### 6.3 更新策略侧边栏配置
- **目标**：为策略文档创建清晰的导航
- **结构**：总览 → 系统概述 → 版本解析

**策略侧边栏结构：**
```javascript
'/docs/策略/': [
  {
    text: '策略总览',
    items: [
      { text: '策略概述', link: '/docs/策略/' }
    ]
  },
  {
    text: '妖底确定买入高抛卖出',
    items: [
      { text: '策略系统概述', link: '/docs/策略/妖底确定买入高抛卖出/' }
    ]
  },
  {
    text: '策略源码解析',
    items: [
      { text: '掘金版策略解析', link: '/docs/策略/妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版/' },
      { text: '通用版策略解析', link: '/docs/策略/妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略/' }
    ]
  }
]
```

#### 6.4 更新工作流侧边栏配置
- **目标**：更新工作流文档的导航链接
- **重点**：修正文件重命名后的链接

**工作流侧边栏更新：**
```javascript
'/workflow/': [
  {
    text: '工作流文档',
    items: [
      { text: '文档生成工作流', link: '/workflow/文档生成工作流' }
    ]
  }
]
```

#### 6.5 创建文档索引
- **目标**：创建完整的文档索引页面
- **文件**：`docs/文档索引.md`
- **内容**：所有md文件的完整列表和VitePress链接

**文档索引内容：**
- 文档结构说明
- 所有md文件的路径和链接
- VitePress导航位置
- 文档统计信息
- 使用建议

#### 6.6 验证配置有效性
- **开发服务器测试**：启动VitePress开发服务器
- **链接有效性检查**：验证所有导航链接正常工作
- **响应式测试**：确认移动端导航正常
- **搜索功能测试**：验证本地搜索功能

**验证命令：**
```bash
# 启动开发服务器
pnpm run dev

# 访问测试
http://localhost:5174/stock/
```

#### 6.7 清理旧文件和配置
- **删除重复文件**：移除旧的Python文件（非文件夹中的）
- **清理死链接**：移除不存在文件的链接
- **更新忽略规则**：配置Python文件的死链接忽略

**清理操作：**
```bash
# 删除重复的Python文件
remove-files [旧文件列表]

# 更新死链接忽略配置
ignoreDeadLinks: [
  /.*\.py$/,  // 忽略Python文件链接
]
```

### 第七阶段：文档优化与完善

#### 7.1 计算逻辑深化
- **多层次分解**：将复杂算法分解为多个清晰步骤
- **数学原理阐述**：解释公式背后的数学逻辑
- **参数影响分析**：说明参数变化对结果的影响

#### 7.2 实际应用指导
- **信号优先级**：区分主要信号和辅助信号
- **组合使用策略**：说明多信号配合使用方法
- **风险控制建议**：提供具体的风险管理措施

### 第八阶段：质量检查与验证

#### 8.1 文件结构验证
- **目录结构检查**：确认所有文件夹创建正确
- **文件完整性检查**：验证README.md和.py文件都在正确位置
- **链接有效性检查**：测试VitePress中的所有导航链接

#### 8.2 文档质量检查
- **内容完整性检查**：确保所有信号都有详细说明
- **逻辑一致性检查**：验证计算逻辑的准确性
- **格式规范性检查**：统一文档格式和风格

### 第九阶段：总结与归档

#### 9.1 创建总结文档
- **算法原理总结**：汇总所有指标的核心算法
- **设计特点分析**：提炼共同的设计模式
- **应用建议整合**：形成统一的使用指导

#### 9.2 工作流程记录
- **更新工作流文档**：记录实际执行的完整流程
- **经验总结**：记录遇到的问题和解决方案
- **优化建议**：提出后续改进的方向

## 工作流程标准化

### 分析阶段检查清单
- [ ] 完成目录结构扫描
- [ ] 读取所有源码文件
- [ ] 识别所有输出信号
- [ ] 提取所有计算变量
- [ ] 分析选股逻辑（如有）

### 文档编写检查清单
- [ ] 创建基础文档结构
- [ ] 编写指标概述
- [ ] 详细描述每个输出信号
- [ ] 解析计算逻辑
- [ ] 提供使用建议
- [ ] 添加注意事项

### 文件结构组织检查清单
- [ ] 为每个Python文件创建同名文件夹
- [ ] 将README.md重命名为index.md并移动到对应文件夹（或直接创建index.md）
- [ ] 移动Python文件到对应文件夹
- [ ] 创建各目录的index.md文件
- [ ] 验证文件夹结构完整性
- [ ] 确保所有目录都使用index.md而不是README.md

### VitePress配置检查清单
- [ ] 更新主导航栏配置
- [ ] 更新指标侧边栏配置（总览+系统概述+详细解析）
- [ ] 更新策略侧边栏配置（总览+系统概述+版本解析）
- [ ] 更新工作流侧边栏配置
- [ ] 创建文档索引页面
- [ ] 验证开发服务器启动正常
- [ ] 测试所有导航链接有效性
- [ ] 验证响应式设计和移动端导航
- [ ] 测试本地搜索功能
- [ ] 删除重复的Python文件
- [ ] 清理死链接和更新忽略规则
- [ ] 验证网站完整功能正常

### 质量控制检查清单
- [ ] 验证计算公式准确性
- [ ] 检查信号描述完整性
- [ ] 确认实际意义解释合理性
- [ ] 验证使用建议实用性
- [ ] 统一文档格式风格
- [ ] 检查文件结构一致性

## 工具和技术要求

### 必需工具
- **代码查看工具**：用于读取和分析源码
- **文档编辑工具**：用于创建和编辑README文件
- **文件管理工具**：用于创建文件夹和移动文件
- **配置编辑工具**：用于更新VitePress配置文件
- **逻辑分析能力**：理解复杂的数学计算和条件逻辑

### 技术要求
- **股票技术分析知识**：理解各种技术指标的原理
- **编程逻辑理解**：能够解析复杂的计算公式
- **文档写作能力**：能够清晰表达技术概念
- **文件系统管理**：熟悉目录结构组织和文件操作
- **VitePress配置**：了解VitePress导航配置语法

## 输出成果

### 文档类型
1. **各指标README.md**：每个指标文件夹的详细说明文档
2. **index.md文件**：各主要目录的导航索引文档
3. **Python源码文件**：与文档放在同一文件夹的源码文件
4. **VitePress配置**：更新后的导航配置文件
5. **工作流文档**：本文档，记录完整的生成流程

### 文档特点
- **详细性**：每个输出信号都有完整说明
- **准确性**：计算逻辑与源码完全对应
- **实用性**：提供具体的使用指导
- **系统性**：形成完整的文档体系
- **结构化**：采用文件夹分类的清晰结构
- **导航性**：通过index.md和VitePress提供完整导航

## 实际执行记录

### 2024年执行情况

#### 处理的文件
**指标文件（7个）：**
1. 擒龙启爆/指标源码.py → 指标源码/index.md + 指标源码.py
2. 擒龙启爆/起爆点选股.py → 起爆点选股/index.md + 起爆点选股.py
3. 金牛暴起/指标源码.py → 指标源码/index.md + 指标源码.py
4. 金牛暴起/妖底确定选股.py → 妖底确定选股/index.md + 妖底确定选股.py
5. 高抛低吸/指标源码.py → 指标源码/index.md + 指标源码.py
6. 高抛低吸/买入选股.py → 买入选股/index.md + 买入选股.py
7. 高抛低吸/卖出选股.py → 卖出选股/index.md + 卖出选股.py

**策略文件（2个）：**
1. 妖底确定买入高抛卖出策略_掘金版.py → 妖底确定买入高抛卖出策略_掘金版/index.md + .py
2. 聚宽妖底确定买入高抛卖出策略.py → 聚宽妖底确定买入高抛卖出策略/index.md + .py

**文件命名规则更新：**
- **2025-01-04 更新**：统一使用 index.md 替代 README.md
- **原因**：符合 VitePress 默认约定，避免死链接问题
- **操作**：将现有 README.md 重命名为 index.md，而不是重复生成内容

#### 创建的文档
- **index.md文档**：9个详细的技术解析文档（原README.md重命名）
- **导航index.md文档**：4个导航索引文档
- **文档索引页面**：1个完整的文档索引
- **文件夹**：9个独立的文件夹结构
- **VitePress配置更新**：完整的导航配置更新

#### VitePress配置更新详情
**主导航更新：**
- 更新工作流链接：`/workflow/文档生成工作流`

**指标侧边栏配置：**
- 指标总览：1个链接
- 指标详解：3个系统概述链接
- 具体指标：7个详细解析链接

**策略侧边栏配置：**
- 策略总览：1个链接
- 策略概述：1个系统概述链接
- 策略解析：2个版本解析链接

**工作流侧边栏配置：**
- 工作流文档：1个更新后的链接

**配置验证：**
- 开发服务器测试：✅ 成功启动
- 链接有效性：✅ 全部有效
- 响应式设计：✅ 移动端正常
- 搜索功能：✅ 本地搜索正常

#### 执行统计
- **总处理文件**：9个Python文件
- **生成文档**：16个Markdown文件（包含文档索引）
- **创建文件夹**：9个独立文件夹
- **VitePress配置项**：4个主要配置区域
- **导航链接总数**：约20个有效链接
- **文档总字数**：约70,000字
- **平均每个文档**：约4,500字

### 经验总结

#### 成功要素
1. **系统化分析**：逐步分析每个输出信号的计算逻辑
2. **结构化组织**：采用统一的文档结构和文件夹组织
3. **详细解释**：对复杂算法进行多层次分解说明
4. **实用指导**：提供具体的使用建议和风险提示
5. **完整配置**：建立完整的VitePress导航和索引体系

#### 遇到的挑战
1. **复杂算法解析**：部分指标包含多重嵌套的复杂计算
2. **文件结构重组**：需要重新组织大量文件和更新配置
3. **一致性维护**：确保所有文档格式和质量的一致性
4. **重复文件问题**：初期复制文件导致重复，需要清理
5. **配置复杂性**：VitePress多层次导航配置较为复杂

#### 解决方案
1. **分步骤解析**：将复杂算法分解为多个清晰的步骤
2. **批量操作**：使用工具进行批量文件操作和配置更新
3. **模板化处理**：建立统一的文档模板确保一致性
4. **移动而非复制**：强调文件移动而不是复制，避免重复
5. **层次化配置**：建立清晰的VitePress导航层次结构

## 持续改进

### 反馈机制
- 根据用户使用反馈优化文档内容
- 定期检查源码变更，更新文档
- 收集实际交易效果，完善使用建议

### 扩展应用
- 可应用于其他类型的技术指标
- 可扩展到更复杂的量化策略文档
- 可形成标准化的文档生成模板

### 优化方向
- **自动化程度**：提高文档生成的自动化程度
- **模板标准化**：建立更完善的文档模板体系
- **质量控制**：建立更严格的质量检查机制

---

**工作流版本**：v2.1
**创建日期**：2025-01-03
**更新日期**：2025-01-04
**适用范围**：股票技术分析指标源码和量化策略文档化
**核心功能**：文件结构组织、VitePress配置更新、文档索引创建
**最新更新**：完整的VitePress配置规则、重复文件清理、导航体系建立
