# 起爆点选股公式详细解析

## 公式概述

起爆点选股公式是擒龙启爆指标体系中的核心选股工具，专门用于筛选处于底部反转关键时刻的股票。该公式通过精密的数学算法，识别股票价格触及历史关键低点并开始反弹的时机，为投资者提供精准的选股信号。

## 选股逻辑

该选股公式基于以下核心理念：
- **历史低点确认**：股票必须触及重要的历史低点
- **反转动力识别**：通过技术指标确认反转动力的出现
- **多重过滤机制**：避免假信号和重复信号的干扰
- **时机精准性**：在最佳时机发出选股信号

## 详细计算步骤

### 第一阶段：基础变量计算

#### 1. 价格相对位置指标 (VAR0-VAR1)

**第一步：60日相对位置计算**
```
VAR0:=(CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(L<PERSON>,60))*100;
```
- 计算当前收盘价在60日价格区间中的相对位置
- 结果为0-100的百分比，0表示60日最低点，100表示60日最高点
- 用于判断股票当前所处的价格区间位置

**第二步：双重平滑处理**
```
VAR3:=SMA(VAR0,3,1);
VAR1:=SMA(VAR3,4,1)-10;
```
- 对相对位置进行3日简单移动平均
- 再进行4日简单移动平均并减去10
- 双重平滑减少噪音，-10调整基准线位置

#### 2. 压力位置指标 (VAR4-VAR2)

**第一步：距离高点位置计算**
```
VAR4:=(HHV(HIGH,60)-CLOSE)/(HHV(HIGH,60)-LLV(LOW,60))*100;
```
- 计算当前收盘价距离60日最高价的相对距离
- 结果越大表示距离高点越远，股票越处于低位

**第二步：压力位置平滑**
```
VAR5:=SMA(VAR4,3,1);
VAR2:=SMA(VAR5,4,1)-90;
```
- 对压力位置进行双重平滑处理
- 减去90调整基准线，使信号更加敏感

### 第二阶段：核心起爆点算法

#### 3. 动力指标计算 (VAR02-VAR8)

**第一步：价格波动强度分析**
```
VAR02:=REF(LOW,1);
VAR03:=SMA(ABS(LOW-VAR02),13,1)/SMA(MAX(LOW-VAR02,0),13,1)*100;
```
- VAR02：前一日最低价
- VAR03：类似RSI的价格波动强度指标
- 分子：13日内最低价变化的绝对值平均
- 分母：13日内最低价上涨幅度的平均
- 结果反映价格波动的强度和方向

**第二步：动力指标调整**
```
VAR04:=EMA(IF(CLOSE*1.2,VAR03*13,VAR03/13),13);
```
- 根据收盘价条件对VAR03进行放大或缩小
- 当CLOSE*1.2为真时，VAR03放大13倍
- 否则VAR03缩小13倍
- 通过13日指数移动平均平滑处理

**第三步：关键价位确定**
```
VAR05:=LLV(LOW,34);
VAR6:=HHV(VAR04,34);
VAR7:=IF(LLV(LOW,56),1,0);
```
- VAR05：34日内最低价，作为关键支撑位
- VAR6：34日内VAR04的最高值
- VAR7：56日内最低价的存在性判断

**第四步：核心动力计算**
```
VAR8:=EMA(IF(LOW<=VAR05,(VAR4+VAR6*2)/2,0),3)/618*VAR7;
```
- 当最低价触及或跌破34日最低价时激活
- 计算公式：(VAR4+VAR6*2)/2，给予VAR6更高权重
- 通过3日EMA平滑，除以618进行标准化
- 乘以VAR7确保在有效期内

#### 4. 信号确认机制 (AA-起爆点)

**第一步：动力上升确认**
```
AA:=VAR8>REF(VAR8,1);
```
- 确认核心动力指标VAR8呈上升趋势
- 表明底部反转动力正在增强

**第二步：历史低点定位**
```
DR:=100;
ZRQ:=3;
DJ:=REF(LLV(L,100),3);
ZD:=REFDATE(DJ,DATE);
XG0:=L=ZD;
```
- DJ：3日前的100日最低价
- ZD：将历史最低价日期对应到当前日期
- XG0：当前最低价是否等于历史关键低点

**第三步：综合信号生成**
```
XGA:=AA&&XG0;
XG1:=XGA>REF(XGA,1);
起爆点:=XG1>REF(XG1,1);
```
- XGA：动力上升且触及历史低点
- XG1：XGA信号的上升确认
- 起爆点：XG1信号的再次上升确认，双重确认机制

### 第三阶段：信号过滤

#### 5. 最终选股条件

**过滤重复信号**
```
FILTER(起爆点=1,5);
```
- 当起爆点信号为1时触发选股
- 5日内过滤重复信号，避免频繁交易
- 确保每个信号都有足够的时间间隔

## 选股信号特点

### 信号触发条件
1. **价格位置**：股票必须处于相对低位
2. **历史确认**：当前价格触及重要历史低点
3. **动力确认**：底部反转动力开始显现
4. **趋势确认**：多重技术指标同时确认
5. **时间过滤**：避免短期内重复信号

### 信号质量特征
- **准确性高**：多重确认机制减少假信号
- **时机精准**：在底部反转的关键时刻发出信号
- **适用性广**：适用于不同类型和规模的股票
- **稳定性强**：通过多重平滑处理减少噪音干扰

## 使用方法

### 选股流程
1. **日常扫描**：每日收盘后运行选股公式
2. **信号确认**：关注起爆点=1的股票
3. **基本面筛选**：结合基本面分析进一步筛选
4. **技术确认**：结合其他技术指标确认
5. **风险评估**：评估个股和市场风险

### 买入时机
- **信号当日**：起爆点信号出现当日可考虑买入
- **次日确认**：等待次日开盘确认后买入
- **回调买入**：信号出现后的小幅回调时买入
- **突破买入**：突破关键阻力位时买入

### 仓位管理
- **初始仓位**：单只股票不超过总资金的10%
- **分批建仓**：可以分2-3次建仓
- **风险控制**：设置5-8%的止损位
- **盈利保护**：达到15-20%收益后设置移动止损

## 注意事项

### 使用限制
1. **市场环境**：在极端熊市中信号可能失效
2. **个股质地**：避免选择基本面恶化的股票
3. **流动性**：确保选中股票有足够的流动性
4. **时间周期**：适合中短期投资，不适合超短线

### 风险提示
1. **假突破风险**：部分信号可能是假突破，需要后续确认
2. **系统性风险**：无法规避整体市场下跌风险
3. **个股风险**：需要关注个股的基本面变化
4. **技术失效**：在特殊市场环境下技术分析可能失效

### 优化建议
1. **结合基本面**：将技术选股与基本面分析相结合
2. **多指标确认**：结合其他技术指标进行确认
3. **动态调整**：根据市场环境调整选股参数
4. **风险管理**：建立完善的风险管理体系

## 实战应用

### 适用场景
- **底部反转**：市场或个股底部反转时期
- **震荡市场**：在震荡市场中寻找反弹机会
- **价值回归**：优质股票价格回归合理区间
- **技术修复**：超跌股票的技术性修复

### 成功要素
1. **耐心等待**：等待高质量信号的出现
2. **严格执行**：严格按照信号执行买卖决策
3. **风险控制**：始终将风险控制放在首位
4. **持续学习**：不断总结经验，优化策略

该选股公式是擒龙启爆指标体系的重要组成部分，通过精密的算法设计和严格的过滤机制，为投资者提供了一个可靠的底部选股工具。正确使用该公式，结合合理的风险管理，可以显著提高投资成功率。
