# 妖底确定选股公式详细解析

## 公式概述

妖底确定选股公式是金牛暴起指标体系中的核心选股工具，专门用于识别股票底部反转的确定性机会。该公式通过多重技术分析手段，筛选出处于底部且具备反转条件的优质股票，为投资者提供高质量的投资标的。

## 选股理念

该选股公式基于以下核心投资理念：
- **底部确认**：股票必须处于相对低位，具备安全边际
- **反转信号**：技术指标显示底部反转迹象
- **动量突破**：价格动量开始从低位向上突破
- **趋势确认**：多重指标确认反转趋势的有效性
- **时机精准**：在最佳时机发出选股信号

## 时间控制机制

```
SJKS:=DATE>=1220816;  // 开始日期
SJJS:=DATE<=1250801;  // 结束日期  
SJTJ:= SJKS AND SJJS; // 时间条件
```

**说明：** 
- 设置了明确的时间范围，确保策略在特定时期内有效
- 可根据市场环境和策略需要调整时间参数
- 时间控制有助于避免在不适宜的市场环境中使用策略

## 详细计算步骤

### 第一阶段：基础指标计算

#### 1. 价格相对位置指标 (C1)

**计算公式：**
```
C1:=((MA(C,30)-L)/MA(C,60))*200;
```

**计算逻辑：**
- MA(C,30)：30日收盘价移动平均线
- L：当日最低价
- MA(C,60)：60日收盘价移动平均线
- 分子：30日均线与当日最低价的差值
- 分母：60日均线作为基准
- 乘以200进行放大，便于判断

**技术含义：**
- 反映当前价格相对于中长期均线的位置关系
- 数值越大，表明价格相对位置越高
- 用于确认股票是否处于合理的买入区间

#### 2. 动量指标 (M2)

**计算公式：**
```
M2:=SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100;
```

**计算逻辑：**
- 分子：7日内上涨幅度的简单移动平均
- 分母：7日内价格变化绝对值的简单移动平均
- 结果类似于RSI指标，反映价格动量强度

**技术含义：**
- 数值范围通常在0-100之间
- 低于20表示超卖状态，高于80表示超买状态
- 用于判断股票的动量状态和反转时机

#### 3. 动量突破过滤 (G1)

**计算公式：**
```
G1:=FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5);
```

**计算逻辑：**
- REF(M2,1)<20：前一日M2指标低于20（超卖状态）
- M2>REF(M2,1)：当日M2指标上升
- FILTER(...,5)：5日内过滤重复信号

**技术含义：**
- 识别从超卖状态开始反弹的时机
- 过滤机制避免频繁交易
- 确保信号的有效性和稳定性

### 第二阶段：核心技术分析

#### 4. 价格压制条件 (TU)

**计算公式：**
```
TU:=C/MA(C,40)<0.74;
```

**计算逻辑：**
- 当前收盘价与40日均线的比值
- 小于0.74表示价格被严重压制

**技术含义：**
- 确保股票处于相对低位
- 提供足够的安全边际
- 为后续反弹提供空间

#### 5. 双重平滑移动平均 (SMMA)

**计算公式：**
```
SMMA:=EMA(EMA(C,5),5);
IM:=EMA(C,5)- REF(EMA(C,5),1);
TSMMA:=SMMA - REF(SMMA,1);
```

**计算逻辑：**
- SMMA：对5日EMA再次进行5日EMA平滑
- IM：5日EMA的变化量
- TSMMA：双重平滑移动平均的变化量

**技术含义：**
- 双重平滑减少价格噪音
- 更准确地反映价格趋势变化
- 为后续计算提供稳定的基础数据

#### 6. 技术形态分析

**价格差异指标 (DIVMA)：**
```
DIVMA:= ABS(EMA(C,5)- SMMA);
```
- 计算5日EMA与双重平滑移动平均的差异
- 反映短期价格与平滑趋势的偏离程度

**振幅条件 (TDJ)：**
```
TDJ:=(H-L)/REF(C,1)>0.05;
```
- 当日振幅超过前一日收盘价的5%
- 确保有足够的价格波动

**振幅频率 (YUL)：**
```
YUL:=COUNT(TDJ,5)>1;
```
- 5日内至少有2天满足振幅条件
- 确保价格活跃度

### 第三阶段：综合信号生成

#### 7. 启动条件确认 (QD)

**计算公式：**
```
QD:=TU AND TDJ AND YUL;
```

**综合条件：**
- TU：价格处于低位（压制状态）
- TDJ：当日有足够振幅
- YUL：近期价格活跃

**技术含义：**
- 确认股票处于底部且开始活跃
- 为反转提供必要条件
- 多重确认提高信号可靠性

#### 8. 确定信号计算 (QR)

**计算公式：**
```
ET:=(IM+TSMMA)/2;
TDF:= POW(DIVMA,1)*POW(ET,3);
NTDF:=TDF/HHV(ABS(TDF),5*3);
QR:=CROSS(NTDF,-0.9);
```

**计算逻辑：**
- ET：综合趋势变化指标
- TDF：技术形态强度指标
- NTDF：标准化的技术形态指标
- QR：NTDF向上突破-0.9的交叉信号

**技术含义：**
- 通过复杂的数学模型识别反转时机
- 标准化处理提高信号的一致性
- 交叉信号确认反转的开始

### 第四阶段：最终选股条件

#### 9. 波段过滤 (BD)

**计算公式：**
```
BD:=FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10);
```

**过滤条件：**
- (G1 AND C1>20)：动量突破且价格位置合理
- OR C>REF(C,1)：或者当日价格上涨
- AND REF(QD,1)：前一日满足启动条件
- FILTER(...,10)：10日内过滤重复信号

#### 10. 选股信号 (XG)

**计算公式：**
```
XG:=FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10);
```

**选股条件：**
- REF(QD,1)：前一日满足启动条件
- (QR OR C>REF(C,1))：确定信号或价格上涨
- MACD.MACD>-1.5：MACD指标不能过度负值
- FILTER(...,10)：10日内过滤重复信号

#### 11. 最终确认 (妖底确定)

**计算公式：**
```
COUNT(XG,13)>=1 AND BD;
```

**最终条件：**
- COUNT(XG,13)>=1：13日内至少出现1次选股信号
- AND BD：同时满足波段过滤条件

## 选股信号特征

### 信号质量特点
1. **安全性高**：多重底部确认，提供安全边际
2. **时机精准**：在反转关键时刻发出信号
3. **过滤充分**：多层过滤机制减少假信号
4. **适应性强**：适用于不同类型的股票

### 适用市场环境
- **震荡市底部**：在震荡市场中寻找反弹机会
- **熊市末期**：识别熊市末期的反转机会
- **个股调整后**：优质股票调整到位后的机会
- **价值回归**：被错杀股票的价值回归机会

## 使用方法

### 选股流程
1. **每日扫描**：收盘后运行选股公式
2. **信号确认**：关注满足条件的股票
3. **基本面筛选**：结合基本面进行二次筛选
4. **技术确认**：用其他指标进行确认
5. **风险评估**：评估个股和市场风险

### 买入策略
- **信号当日**：可以考虑在信号出现当日买入
- **次日确认**：等待次日开盘确认后买入
- **回调买入**：等待小幅回调后买入
- **分批建仓**：可以分2-3次建仓

### 仓位管理
- **初始仓位**：单只股票不超过总资金的15%
- **总体仓位**：妖底确定股票总仓位不超过50%
- **风险控制**：设置8%的止损位
- **盈利保护**：获利20%后设置移动止损

## 注意事项

### 使用限制
1. **时间范围**：注意公式的时间有效期设置
2. **市场环境**：在极端市场环境下谨慎使用
3. **个股质地**：避免基本面恶化的股票
4. **流动性要求**：确保股票有足够的流动性

### 风险提示
1. **技术分析局限性**：技术分析不能预测所有市场变化
2. **假突破风险**：部分信号可能是假突破
3. **系统性风险**：无法规避整体市场风险
4. **时滞性**：信号可能存在一定的时滞

### 优化建议
1. **参数调整**：根据市场环境调整时间参数
2. **多指标结合**：结合其他技术指标确认
3. **基本面结合**：将技术选股与基本面分析结合
4. **动态管理**：根据市场变化动态调整策略

## 实战应用技巧

### 提高成功率的方法
1. **耐心等待**：等待高质量信号，不要急于操作
2. **严格执行**：严格按照信号执行，不要主观判断
3. **风险优先**：始终将风险控制放在第一位
4. **持续学习**：不断总结经验，优化使用方法

### 常见误区避免
1. **频繁交易**：避免因为信号频繁而过度交易
2. **忽视基本面**：不要完全依赖技术信号
3. **重仓操作**：避免在单一信号上重仓
4. **情绪化操作**：保持理性，避免情绪化决策

妖底确定选股公式是一个经过精心设计的技术选股工具，通过多重技术分析手段，为投资者提供了识别底部反转机会的有效方法。正确使用该公式，结合合理的风险管理和基本面分析，可以显著提高投资的成功率和收益水平。
