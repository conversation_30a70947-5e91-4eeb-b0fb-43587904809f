
import numpy as np

def MACD(close, fastperiod=12, slowperiod=26, signalperiod=9):
    
    """
    功能：MACD函数，跟通达信、同花顺完全一致
    用法：
            MACD( data['close']             )
            MACD( data['close'].tolist()    )
            MACD( np.array(data['close'])   )
    输入：pd.Series、list、np.array
    输出：
        (np.array, np.array, np.array)
        (DIFF, DEA, MACD)
    """
        
    def EMA(plist,N):
        ema_list = []
        ema = plist[0]
        ema_list.append(ema)
        for i in range(1,len(plist)):
            ema=(2*plist[i]+(N-1)*ema)/(N+1)
            ema_list.append(ema)
        return np.array(ema_list)
    
    fast = EMA(close, fastperiod)
    slow = EMA(close, slowperiod)
    DIFF = fast - slow
    DEA  = EMA(DIFF, signalperiod)
    MACD = 2*( DIFF - DEA)
    return DIFF, DEA, MACD