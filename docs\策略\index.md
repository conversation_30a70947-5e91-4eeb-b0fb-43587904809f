# 策略总览

本目录包含了各种股票交易策略的实现和说明文档。每个策略都提供了详细的使用说明和多个平台的实现版本。

## 策略列表

### 妖底确定买入高抛卖出策略

一个基于技术指标的量化交易策略，通过识别股票的底部形态和高抛低吸点位来进行买卖决策。

**相关文档：**
- [策略概述](./妖底确定买入高抛卖出/) - 策略的基本原理和逻辑说明
- [掘金版使用说明](./妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版/) - 在掘金平台上的部署和使用指南
- [通用版使用说明](./妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略/) - 通用版本的配置和使用方法

**代码实现：**
- [掘金版策略代码](./妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版.py)
- [聚宽版策略代码](./妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略.py)
- [AKShare数据源示例](./妖底确定买入高抛卖出/示例实现_akshare数据源.py)

## 策略特点

- **多平台支持**：提供掘金、聚宽等主流量化平台的实现版本
- **灵活配置**：支持参数调整以适应不同市场环境
- **完整文档**：每个策略都有详细的使用说明和代码注释
- **实战验证**：策略经过实际交易验证，具有一定的实用性

## 使用建议

1. **风险控制**：任何策略都存在风险，请在充分理解策略逻辑后谨慎使用
2. **参数调优**：根据不同的市场环境和个人风险偏好调整策略参数
3. **回测验证**：在实盘使用前，建议先进行充分的历史回测
4. **持续优化**：根据市场变化和策略表现持续优化和改进

## 技术支持

如有问题或建议，请参考各策略目录下的详细文档，或查看相关的指标实现说明。
