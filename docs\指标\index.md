# 指标计算逻辑详细总结

## 概述
本文档详细总结了三个主要指标的核心计算逻辑，包括每个信号的数学原理、触发条件和实际意义。

## 一、擒龙启爆指标

### 核心算法原理

#### 1. 起爆点算法
**多层次确认机制：**
- **价格波动强度分析**：使用类RSI算法计算价格下跌的相对强度
- **波动指标调整**：根据收盘价存在性动态调整波动幅度
- **历史低点定位**：通过100日最低价确定关键支撑位
- **综合强度指标**：结合当前波动、历史峰值和黄金分割比例
- **双重确认机制**：连续两日信号强化，避免噪音干扰

**关键公式：**
```
VAR8 = EMA(IF(LOW<=LLV(LOW,34),(VAR4+HHV(VAR04,34)*2)/2,0),3)/618*VAR7
起爆点 = (VAR8上升 && 触及历史低点) 的二次确认
```

#### 2. 启爆器算法
**量价同步突破模型：**
- **成交量爆发**：量比变化突破500倍基准
- **价格加速**：涨幅变化突破10%基准
- **双重确认**：量价必须同时满足突破条件

**数学模型：**
```
量比加速度 = (当日量比 - 前日量比)
价格加速度 = (当日涨幅 - 前日涨幅)
启爆器 = CROSS(量比加速度,500) && CROSS(价格加速度,10)
```

## 二、金牛暴起指标

### 核心算法原理

#### 1. 主力进场算法
**开收盘价差异分析模型：**
- **收盘价相对位置**：在75日高低点区间的位置，经过20日和15日双重平滑
- **开盘价相对位置**：同样计算方法，但基于开盘价
- **资金推动确认**：收盘价指标低于前日开盘价指标，表示盘中有资金推动

**核心逻辑：**
```
GUB6 = 100-3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(...)
GUB7 = 100-3*SMA((OPEN-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(...)
主力进场 = GUB6<REF(GUB7,1) && 成交量放大 && 价格上涨
```

#### 2. 金牛暴起算法
**强势形态识别模型：**
- **涨幅筛选**：必须超过5%的显著涨幅
- **形态质量**：上影线小于1%，确保买盘强劲
- **时间过滤**：45日内只出现一次，确保信号独特性

**筛选条件：**
```
涨幅条件 = (CLOSE/REF(CLOSE,1)) > 1.05
形态条件 = (HIGH/CLOSE) < 1.01
金牛暴起 = FILTER(涨幅条件 && 形态条件 && 基础上涨, 45)
```

## 三、高抛低吸指标

### 核心算法原理

#### 1. 秘籍线算法
**动态区间相对位置模型：**
- **典型价格**：给收盘价更大权重的价格计算方法
- **不对称区间**：支撑看长期(21日)，阻力看短期(8日)
- **相对位置**：在动态区间内的百分比位置
- **平滑处理**：9日EMA减少波动干扰

**计算公式：**
```
典型价格 = (2*收盘价 + 最高价 + 最低价) / 4
动态区间 = [21日最低价, 8日最高价]
相对位置 = (典型价格 - 21日最低价) / (8日最高价 - 21日最低价) * 100
秘籍线 = EMA(相对位置, 9)
```

#### 2. 探秘线算法
**加权平滑模型：**
- **时间权重**：前日67%，当日33%的权重分配
- **二次平滑**：EMA(2)进一步稳定信号
- **快慢线组合**：形成交叉信号系统

**计算逻辑：**
```
加权值 = 0.667*前日秘籍线 + 0.333*当日秘籍线
探秘线 = EMA(加权值, 2)
```

#### 3. 逃顶算法
**多重平滑顶部识别模型：**
- **价格位置**：在14日高低点区间的相对位置
- **三线系统**：3日快线、7日中线、5日慢线
- **顶部确认**：慢线上穿快线且在高位区域
- **时间窗口**：2日内保持逃顶提示

## 算法设计特点总结

### 1. 多重确认机制
- 所有关键信号都采用多重条件确认
- 避免单一指标的假信号
- 提高信号的可靠性和可操作性

### 2. 动态参数调整
- 根据市场状态动态调整计算参数
- 适应不同市场环境的变化
- 提高指标的适应性

### 3. 时间过滤机制
- 通过FILTER函数避免频繁信号
- 确保信号的独特性和重要性
- 减少过度交易的风险

### 4. 量价结合分析
- 同时考虑价格和成交量变化
- 确保信号的真实性和有效性
- 避免纯技术指标的局限性

### 5. 平滑处理技术
- 多层次的平滑处理减少噪音
- 保持信号的稳定性和连续性
- 提高指标的实用性

## 实际应用建议

### 1. 信号优先级
- **强信号**：起爆点、金牛暴起、主力进场
- **辅助信号**：启爆器、逃顶、探秘线交叉
- **过滤信号**：成交量确认、时间窗口验证

### 2. 组合使用策略
- 多个指标信号同时出现时，可信度更高
- 单一指标信号需要其他技术分析确认
- 结合基本面分析，避免技术陷阱

### 3. 风险控制要点
- 严格按照信号操作，避免主观判断
- 设置合理的止损止盈位
- 控制单次操作的仓位比例
- 关注市场整体环境的影响
