# 妖底确定买入高抛卖出策略系统

## 策略概述

妖底确定买入高抛卖出策略是一个完整的量化交易策略系统，结合了妖底确定选股指标和高抛低吸卖出指标，通过精密的技术分析算法，实现自动化的股票买卖操作。该策略旨在捕捉股票底部反转机会并在高位及时止盈，为投资者提供稳定的投资收益。

## 系统组成

### 掘金版策略
- **文件位置**: [妖底确定买入高抛卖出策略_掘金版](./妖底确定买入高抛卖出策略_掘金版/)
- **Python文件**: [妖底确定买入高抛卖出策略_掘金版.py](./妖底确定买入高抛卖出策略_掘金版/妖底确定买入高抛卖出策略_掘金版.py)
- **功能说明**: 专门为掘金量化平台设计的完整量化交易策略，支持回测和实盘交易

### 通用版策略
- **文件位置**: [聚宽妖底确定买入高抛卖出策略](./聚宽妖底确定买入高抛卖出策略/)
- **Python文件**: [聚宽妖底确定买入高抛卖出策略.py](./聚宽妖底确定买入高抛卖出策略/聚宽妖底确定买入高抛卖出策略.py)
- **功能说明**: 采用面向对象设计的通用量化交易策略框架，可适配多种数据源和交易平台

## 策略特点

### 核心优势
- **双指标融合**：结合妖底确定和高抛低吸两大核心指标
- **自动化执行**：完全自动化的买卖决策和执行
- **风险可控**：内置多重风险控制机制
- **平台适配**：支持多种量化交易平台
- **实盘验证**：经过实盘交易验证的成熟策略

### 技术特色
- **精准时机**：每日收盘前5分钟执行，避免盘中噪音
- **仓位管理**：十分之一仓位买入，支持加仓操作
- **信号过滤**：多重过滤机制，减少假信号干扰
- **容错设计**：内置异常处理和备用算法

## 策略逻辑

### 核心交易逻辑
1. **买入逻辑**：使用妖底确定选股公式筛选买入标的
2. **卖出逻辑**：使用高抛低吸卖出公式判断卖出时机
3. **时间控制**：每日14:55执行策略，避免收盘前波动
4. **仓位管理**：单次买入十分之一仓位，可多次加仓

### 执行流程
```
策略初始化 → 定时任务设置 → 每日14:55执行 → 检查卖出信号 → 检查买入信号 → 执行交易 → 记录日志
```

## 买入信号详解

### 妖底确定选股条件
1. **价格压制条件**：C/MA(C,40)<0.74，确保股票处于相对低位
2. **动量突破分析**：M2指标从超卖状态开始反弹
3. **技术形态确认**：通过SMMA、TDJ等多重技术指标确认
4. **MACD确认**：MACD指标不能过度负值
5. **时间窗口确认**：13日内至少出现1次选股信号

### 买入执行机制
- **股票池筛选**：排除北证、ST、停牌股票
- **资金管理**：单次买入不超过总资金的10%
- **加仓机制**：已持有股票可以继续加仓
- **风险控制**：设置最小买入金额限制

## 卖出信号详解

### 高抛低吸卖出条件
1. **MJ指标计算**：基于动态区间的相对位置指标
2. **卖出阈值**：MJ指标从上方跌破80时触发
3. **趋势确认**：确认上升动力开始衰竭
4. **及时止盈**：在相对高位及时保护收益

### 卖出执行机制
- **全仓卖出**：卖出信号时全仓卖出该股票
- **优先执行**：卖出检查优先于买入检查
- **风险控制**：及时止盈，保护收益

## 风险控制机制

### 仓位控制
- **单次买入**：不超过总资金的10%
- **总仓位**：通过买入次数控制总仓位
- **最大持仓**：限制最大持仓股票数量
- **资金门槛**：设置最小可用资金要求

### 交易控制
- **时间控制**：固定在收盘前5分钟执行
- **信号过滤**：多重过滤机制避免假信号
- **异常处理**：完善的异常处理机制
- **日志记录**：详细的交易日志记录

## 平台适配

### 掘金平台版本
- **数据接口**：使用掘金API获取数据
- **交易接口**：使用掘金交易接口执行交易
- **回测支持**：支持掘金平台回测功能
- **实盘交易**：支持掘金平台实盘交易

### 通用版本
- **抽象接口**：通过抽象接口实现平台无关性
- **数据提供者**：支持聚宽、AKShare、掘金等数据源
- **交易提供者**：支持多种交易平台接口
- **高度可扩展**：易于扩展和定制功能

## 使用方法

### 掘金平台部署
1. 将策略代码复制到掘金平台
2. 设置策略参数（如需要）
3. 选择回测或实盘模式
4. 启动策略运行

### 通用版本使用
1. 创建对应平台的数据和交易提供者
2. 配置策略参数
3. 初始化策略实例
4. 运行日常策略

## 策略优化建议

### 参数优化
- **仓位比例**：根据风险偏好调整
- **交易时间**：根据市场特点调整
- **过滤周期**：根据信号频率调整

### 功能扩展
- **止损机制**：添加固定止损功能
- **分批卖出**：实现分批减仓功能
- **市场过滤**：添加大盘趋势过滤
- **行业轮动**：结合行业轮动策略

### 风险管理
- **最大回撤控制**：设置最大回撤限制
- **单股仓位限制**：限制单只股票最大仓位
- **总仓位控制**：根据市场环境调整总仓位

## 注意事项

### 使用限制
- 适用于A股市场
- 需要对应平台环境
- 建议在模拟环境充分测试后再实盘使用

### 风险提示
- 技术分析存在局限性
- 市场极端情况下可能失效
- 需要持续监控和优化

### 维护建议
- 定期检查策略运行状态
- 根据市场变化调整参数
- 持续优化和改进策略

## 实盘应用经验

### 最佳实践
- 充分回测验证策略有效性
- 小资金开始，逐步增加投入
- 严格按照策略信号执行
- 定期分析策略表现

### 常见问题
- **信号延迟**：确保数据及时更新
- **执行偏差**：注意交易时间和流动性
- **参数失效**：根据市场变化调整参数

该策略系统是一个经过实践验证的量化交易解决方案，通过科学的技术分析和严格的风险控制，为投资者提供了一个可靠的自动化交易工具。正确使用和持续优化，可以在控制风险的前提下获得稳定的投资收益。
