"""
妖底确定买入高抛卖出量化策略 - 聚宽平台版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位
3. 收盘前检测持仓股票是否触发卖出信号
4. 如有卖出信号则清仓
"""

# 导入聚宽函数库
from jqdata import *
import math
import pandas as pd

# 初始化函数，设定基准等等
def initialize(context):
    # 设定沪深300作为基准
    set_benchmark('000300.XSHG')
    # 开启动态复权模式(真实价格)
    set_option('use_real_price', True)
    # 输出内容到日志
    log.info('妖底确定买入高抛卖出策略初始化')
    
    # 股票类每笔交易时的手续费设置
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003, close_commission=0.0003, min_commission=5), type='stock')
    
    # 策略参数
    g.position_ratio = 0.1  # 单次买入仓位比例（十分之一）
    g.max_positions = 10  # 最大持仓数量
    
    # 运行函数设置
    # 收盘前10分钟运行选股和交易
    run_daily(trade_strategy, time='14:57', reference_security='000300.XSHG')
    # 收盘后运行
    run_daily(after_market_close, time='after_close', reference_security='000300.XSHG')

def get_holding_stocks(context):
    """
    安全地获取当前持仓股票列表，避免访问空position产生警告
    """
    holding_stocks = []
    for stock in context.portfolio.positions:
        position = context.portfolio.positions[stock]
        if position.closeable_amount > 0:
            holding_stocks.append(stock)
    return holding_stocks

def calculate_yaodi_signal(context, stock):
    """
    计算妖底确定买入信号 - 完整版本，与 '妖底确定选股.py' 公式严格一致
    :param context: 聚宽上下文
    :param stock: 股票代码
    :return: 是否满足妖底确定条件
    """
    try:
        # 获取足够的历史数据, MACD需要约35天, 其他指标最长需要60天
        hist_data = get_bars(stock, count=100, unit='1d',
                           fields=['close', 'high', 'low', 'volume'])

        if len(hist_data) < 100:
            return False

        # 转换为pandas Series便于计算
        C = pd.Series(hist_data['close'])
        H = pd.Series(hist_data['high'])
        L = pd.Series(hist_data['low'])

        # --- 核心指标计算 ---

        # C1指标
        MA30 = C.rolling(30).mean()
        MA60 = C.rolling(60).mean()
        C1 = ((MA30 - L) / MA60) * 200

        # M2指标 (使用ewm模拟SMA)
        price_changes = C.diff()
        abs_changes = price_changes.abs()
        positive_changes = price_changes.where(price_changes > 0, 0)
        # SMA(X, N, M=1) => X.ewm(alpha=M/N, adjust=False).mean()
        M2 = positive_changes.ewm(alpha=1/7, adjust=False).mean() / abs_changes.ewm(alpha=1/7, adjust=False).mean() * 100

        # G1条件: 5日内曾满足 (M2昨日<20 且 M2今日>M2昨日)
        g1_base = (M2.shift(1) < 20) & (M2 > M2.shift(1))
        G1 = g1_base.rolling(5).sum() > 0

        # TU条件：超跌状态
        MA40 = C.rolling(40).mean()
        TU = C / MA40 < 0.74

        # TDJ条件：振幅大于5%
        TDJ = (H - L) / C.shift(1) > 0.05

        # YUL条件: 5日内TDJ次数>1
        YUL = TDJ.rolling(5).sum() > 1

        # QD启动条件
        QD = TU & TDJ & YUL

        # NTDF相关计算
        EMA5 = C.ewm(span=5, adjust=False).mean()
        SMMA = EMA5.ewm(span=5, adjust=False).mean()
        IM = EMA5.diff()
        TSMMA = SMMA.diff()
        DIVMA = (EMA5 - SMMA).abs()
        ET = (IM + TSMMA) / 2
        TDF = DIVMA * (ET ** 3) # POW(DIVMA,1) is just DIVMA
        NTDF = TDF / TDF.abs().rolling(15).max() # HHV(ABS(TDF), 5*3)

        # QR确定条件 - NTDF向上突破-0.9
        QR = (NTDF > -0.9) & (NTDF.shift(1) <= -0.9)

        # MACD 计算
        DIF = C.ewm(span=12, adjust=False).mean() - C.ewm(span=26, adjust=False).mean()
        DEA = DIF.ewm(span=9, adjust=False).mean()
        MACD = (DIF - DEA) * 2

        # XG选股条件: 10日内曾满足 (昨日QD为真 AND (今日QR为真 或 今日收盘价上涨) AND MACD>-1.5)
        xg_base = QD.shift(1) & (QR | (C > C.shift(1))) & (MACD > -1.5)
        XG = xg_base.rolling(10).sum() > 0

        # BD波段条件: 10日内曾满足 ((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1))
        # 注意: G1 AND C1>20 OR C>REF(C,1) 的优先级是 (G1 AND C1>20) OR (C>REF(C,1))
        bd_base = ((G1 & (C1 > 20)) | (C > C.shift(1))) & QD.shift(1)
        BD = bd_base.rolling(10).sum() > 0

        # 最终妖底确定条件: COUNT(XG,13)>=1 AND BD
        # COUNT(XG,13)>=1: 13日内曾有XG信号
        final_cond1 = XG.rolling(13).sum() >= 1
        # BD: BD信号今日为真
        final_cond2 = BD

        # --- 信号判断 ---
        # 确保所有计算结果序列有足够的长度
        if len(final_cond1) == 0 or len(final_cond2) == 0:
            return False

        # 获取最后一个值
        last_signal1 = final_cond1.iloc[-1] if not pd.isna(final_cond1.iloc[-1]) else False
        last_signal2 = final_cond2.iloc[-1] if not pd.isna(final_cond2.iloc[-1]) else False

        yaodi_signal = bool(last_signal1 and last_signal2)

        # 调试信息
        if yaodi_signal:
            log.info(f"{stock} 妖底确定信号触发: XG_13日计数满足={last_signal1}, BD满足={last_signal2}")

        return yaodi_signal

    except Exception as e:
        log.error(f"计算妖底确定信号时出错 {stock}: {e}")
        return False

def calculate_sell_signal(context, stock):
    """
    计算高抛低吸卖出信号 - 完整版本，与卖出选股公式一致
    :param context: 聚宽上下文
    :param stock: 股票代码
    :return: 是否满足卖出条件
    """
    try:
        # 获取足够的历史数据（需要75天用于VAR1计算）
        hist_data = get_bars(stock, count=75, unit='1d',
                           fields=['close', 'high', 'low'])

        if len(hist_data) < 75:
            return False

        # 转换为pandas Series便于计算
        C = pd.Series(hist_data['close'])
        H = pd.Series(hist_data['high'])
        L = pd.Series(hist_data['low'])

        # 参数设置
        N1, N2 = 21, 8

        # 计算完整的技术指标体系
        # VAR1指标（虽然在最终公式中未直接使用，但保持完整性）
        LLV75 = L.rolling(75).min()
        HHV75 = H.rolling(75).max()
        base_indicator = (C - LLV75) / (HHV75 - LLV75) * 100
        sma1 = base_indicator.rolling(20).mean()
        sma2 = sma1.rolling(15).mean()
        VAR1 = 3 * sma1 - 2 * sma2

        # VAR2-VAR7指标（保持完整性）
        LLV26 = L.rolling(26).min()
        HHV26 = H.rolling(26).max()
        VAR2 = (C - LLV26) / (HHV26 - LLV26) * 100
        VAR3 = VAR2.rolling(3).mean().rolling(3).mean()
        VAR4 = VAR3.ewm(span=5).mean()
        VAR5 = LLV26
        VAR6 = H.rolling(34).max()
        VAR7 = ((C - VAR5) / (VAR6 - VAR5) * 4).ewm(span=4).mean() * 25

        # VAR8：典型价格
        VAR8 = (2 * C + H + L) / 4

        # VAR9和VAR10
        VAR9 = L.rolling(N1).min()  # 21日最低价
        VAR10 = H.rolling(N2).max()  # 8日最高价

        # VAR2W相关指标
        HHV14 = H.rolling(14).max()
        LLV14 = L.rolling(14).min()
        VAR2W = 100 - 100 * (HHV14 - C) / (HHV14 - LLV14)
        MW = VAR2W.ewm(span=3).mean()
        VAR3W = VAR2W.ewm(span=7).mean()
        M1 = VAR3W.ewm(span=5).mean()

        # MB1和MG1条件
        MB1 = (MW > M1) & (MW.shift(1) <= M1.shift(1)) & (M1 < 20)
        MG1_condition = (M1 > MW) & (M1.shift(1) <= MW.shift(1)) & (MW.shift(1) > 80)

        # 核心指标MJ：使用EMA(9)平滑
        raw_mj = (VAR8 - VAR9) / (VAR10 - VAR9) * 100
        MJ = raw_mj.ewm(span=9).mean()

        # TM指标（探秘）
        TM = (0.667 * MJ.shift(1) + 0.333 * MJ).ewm(span=2).mean()

        # 卖出条件：CROSS(80,MJ) - MJ从上方向下跌破80
        sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)

        # 安全检查
        if len(sell_signal) == 0:
            return False

        # 获取最新的信号
        latest_signal = sell_signal.iloc[-1] if not pd.isna(sell_signal.iloc[-1]) else False

        # 调试信息（只在信号为True时输出）
        if latest_signal:
            log.info(f"{stock} 卖出信号触发: MJ当前={MJ.iloc[-1]:.2f}, MJ前值={MJ.iloc[-2]:.2f}")

        return bool(latest_signal)

    except Exception as e:
        log.error(f"计算卖出信号时出错 {stock}: {e}")
        return False

def get_stock_pool(context):
    """
    获取股票池（全市场A股，排除北证、ST、停牌等）
    """
    try:
        # 获取所有A股股票
        stocks = get_all_securities(['stock']).index.tolist()

        # 过滤条件
        current_data = get_current_data()
        filtered_stocks = []

        for stock in stocks:
            # 排除北证股票（北证股票代码格式：8XXXXX.XBSE 或 43XXXX.XBSE）
            stock_code = stock.split('.')[0]  # 获取股票代码部分
            if stock_code.startswith('8') or stock_code.startswith('43') or '.XBSE' in stock:
                continue

            # 只保留沪深A股（.XSHE 和 .XSHG）
            if not (stock.endswith('.XSHE') or stock.endswith('.XSHG')):
                continue

            # 排除ST股票、停牌股票、价格异常股票
            if (not current_data[stock].is_st and
                not current_data[stock].paused and
                current_data[stock].last_price > 0):
                filtered_stocks.append(stock)

        log.info(f"股票池筛选完成，共 {len(filtered_stocks)} 只股票（已排除北证、ST、停牌股票）")

        # 返回全部符合条件的股票，不再限制数量
        return filtered_stocks

    except Exception as e:
        log.error(f"获取股票池时出错: {e}")
        return []

def trade_strategy(context):
    """
    主要交易策略函数
    """
    log.info('开始执行交易策略')
    
    # 1. 检查当前持仓，执行卖出逻辑
    check_sell_signals(context)
    
    # 2. 如果持仓数量未达到上限，执行买入逻辑
    current_holdings = get_holding_stocks(context)
    current_positions = len(current_holdings)

    if current_positions < g.max_positions:
        check_buy_signals(context)
    
    log.info(f'策略执行完成，当前持仓数量: {current_positions}')

def check_sell_signals(context):
    """
    检查卖出信号
    """
    # 获取当前持仓股票
    holding_stocks = get_holding_stocks(context)

    # 对持有的股票检查卖出信号
    for stock in holding_stocks:
        try:
            if calculate_sell_signal(context, stock):
                log.info(f"触发卖出信号，清仓 {stock}")
                # 清仓
                order_target(stock, 0)
        except Exception as e:
            log.error(f"检查卖出信号时出错 {stock}: {e}")

def check_buy_signals(context):
    """
    检查买入信号
    """
    # 获取股票池
    stock_pool = get_stock_pool(context)
    log.info(f"股票池大小: {len(stock_pool)}")

    # 当前可用资金
    available_cash = context.portfolio.available_cash

    # 单次买入金额（十分之一仓位）
    single_position_value = context.portfolio.total_value * g.position_ratio
    log.info(f"可用资金: {available_cash:.2f}, 单次买入金额: {single_position_value:.2f}")

    buy_candidates = []
    
    # 获取当前实际持仓的股票列表
    current_holdings = get_holding_stocks(context)

    # 遍历股票池中所有股票，寻找买入信号
    log.info(f"开始遍历股票池中所有 {len(stock_pool)} 只股票...")
    for stock in stock_pool:
        try:
            # 如果已经持有该股票，跳过
            if stock in current_holdings:
                continue

            # 检查妖底确定信号
            if calculate_yaodi_signal(context, stock):
                current_price = get_current_data()[stock].last_price
                if current_price > 0:
                    buy_candidates.append((stock, current_price))
                    log.info(f"找到买入候选: {stock}, 价格: {current_price:.2f}")

        except Exception as e:
            log.error(f"检查买入信号时出错 {stock}: {e}")
            continue

    log.info(f"全市场扫描完成，找到 {len(buy_candidates)} 个买入候选")
    
    # 按价格排序，优先买入低价股（可根据需要调整排序逻辑）
    buy_candidates.sort(key=lambda x: x[1])
    
    # 执行买入（限制单次最多买入3只股票）
    bought_count = 0
    for stock, price in buy_candidates:
        if available_cash >= single_position_value and bought_count < 3:
            log.info(f"触发买入信号，买入 {stock}，价格: {price:.2f}")
            # 买入指定金额
            order_value(stock, single_position_value)
            available_cash -= single_position_value
            bought_count += 1
        else:
            break

## 收盘后运行函数
def after_market_close(context):
    """
    收盘后运行函数
    """
    log.info(f'收盘后运行时间: {context.current_dt.time()}')
    
    # 获得当天所有成交记录
    trades = get_trades()
    for _trade in trades.values():
        log.info(f'成交记录: {_trade}')
    
    # 输出当前持仓信息
    positions = context.portfolio.positions
    total_value = context.portfolio.total_value
    available_cash = context.portfolio.available_cash
    
    # 获取实际持仓股票
    holding_stocks = get_holding_stocks(context)

    log.info(f'当前总资产: {total_value:.2f}')
    log.info(f'可用资金: {available_cash:.2f}')
    log.info(f'持仓数量: {len(holding_stocks)}')

    # 输出持仓详情
    for stock in holding_stocks:
        position = positions[stock]
        current_price = get_current_data()[stock].last_price
        profit_loss = (current_price - position.avg_cost) * position.closeable_amount
        profit_rate = (current_price - position.avg_cost) / position.avg_cost * 100
        log.info(f'持仓 {stock}: 数量={position.closeable_amount}, '
                f'成本={position.avg_cost:.2f}, 现价={current_price:.2f}, '
                f'盈亏={profit_loss:.2f} ({profit_rate:.2f}%)')
    
    log.info('一天结束')
    log.info('##############################################################')
