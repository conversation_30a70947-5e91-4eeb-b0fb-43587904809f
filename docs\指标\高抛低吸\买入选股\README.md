# 高抛低吸买入选股公式详细解析

## 公式概述

高抛低吸买入选股公式是基于高抛低吸指标体系的核心选股工具，专门用于识别处于相对低位且具备反弹潜力的股票。该公式通过精密的相对位置算法，筛选出最适合买入的投资标的，为波段操作提供高质量的买入信号。

## 选股理念

该买入选股公式基于以下核心投资理念：
- **相对低位原则**：选择在动态区间内处于相对低位的股票
- **反弹时机把握**：在股票开始从底部反弹时及时捕捉
- **动态区间适应**：根据市场变化动态调整判断标准
- **多重平滑过滤**：通过多层平滑算法减少噪音干扰
- **简洁高效执行**：用最简洁的条件实现最有效的选股

## 参数设置

```
N1:=21;  // 最低价计算周期
N2:=8;   // 最高价计算周期
```

**参数说明：**
- **N1=21**：用于计算21日内最低价，确定区间下限
- **N2=8**：用于计算8日内最高价，确定区间上限
- **周期组合**：21日和8日的组合能够很好地平衡敏感性和稳定性

## 详细计算步骤

### 第一阶段：基础数据计算

#### 1. 长期趋势指标 (VAR1)

**计算公式：**
```
VAR1:=3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
```

**计算逻辑：**
- 第一步：计算收盘价在75日区间内的相对位置
- 第二步：进行20日简单移动平均
- 第三步：对结果再进行15日简单移动平均
- 第四步：按3:2的权重进行加权计算

**技术含义：**
- 反映股票在长期区间内的相对位置
- 通过双重平滑减少短期波动影响
- 加权计算突出近期趋势变化

#### 2. 中期位置指标 (VAR2-VAR4)

**计算步骤：**
```
VAR2:=(CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100;
VAR3:=SMA(SMA(VAR2,3,1),3,1);
VAR4:=EMA(VAR3,5);
```

**计算逻辑：**
- VAR2：收盘价在26日区间内的相对位置
- VAR3：对VAR2进行双重3日简单移动平均
- VAR4：对VAR3进行5日指数移动平均

**技术含义：**
- 反映中期价格位置变化
- 多重平滑提高信号稳定性
- 为后续计算提供基础数据

#### 3. 动态区间指标 (VAR5-VAR7)

**计算步骤：**
```
VAR5:=LLV(LOW,26);
VAR6:=HHV(HIGH,34);
VAR7:=EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25;
```

**计算逻辑：**
- VAR5：26日内最低价，作为支撑位
- VAR6：34日内最高价，作为阻力位
- VAR7：收盘价在动态区间内的相对位置，经过放大和平滑处理

**技术含义：**
- 建立动态的支撑阻力区间
- 通过放大系数增强信号敏感性
- EMA平滑减少噪音干扰

### 第二阶段：核心选股算法

#### 4. 加权价格计算 (VAR8)

**计算公式：**
```
VAR8:=(2*C+H+L)/4;
```

**计算逻辑：**
- 对收盘价给予双倍权重
- 结合最高价和最低价
- 计算加权平均价格

**技术含义：**
- 更好地反映当日价格重心
- 收盘价双倍权重体现其重要性
- 为相对位置计算提供准确基础

#### 5. 动态区间定义 (VAR9-VAR10)

**计算公式：**
```
VAR9:=LLV(LOW,N1);   // 21日内最低价
VAR10:=HHV(HIGH,N2); // 8日内最高价
```

**计算逻辑：**
- VAR9：21日内最低价，作为区间下限
- VAR10：8日内最高价，作为区间上限
- 形成动态的价格区间

**技术含义：**
- 建立适应性强的动态区间
- 21日最低价提供较长期的支撑参考
- 8日最高价提供较短期的阻力参考

#### 6. 辅助确认指标 (VAR2W-M1)

**计算步骤：**
```
VAR2W:=100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14));
MW:= EMA(VAR2W,3);
VAR3W:=EMA(VAR2W,7);
M1:= EMA(VAR3W,5);
```

**计算逻辑：**
- VAR2W：收盘价在14日区间内的相对位置（反向计算）
- MW：3日EMA平滑
- VAR3W：7日EMA平滑
- M1：对VAR3W进行5日EMA平滑

**技术含义：**
- 提供多时间周期的位置确认
- 多重平滑提高信号质量
- 为最终选股提供辅助判断

### 第三阶段：核心指标计算

#### 7. 秘籍指标 (秘籍)

**计算公式：**
```
秘籍:=EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9);
```

**计算逻辑：**
- 分子：加权价格与区间下限的差值
- 分母：区间上限与下限的差值
- 结果：加权价格在动态区间内的相对位置百分比
- 平滑：通过9日EMA平滑处理

**技术含义：**
- 核心选股指标，反映股票在动态区间内的位置
- 数值范围0-100，越小表示越接近底部
- EMA平滑减少短期噪音，提高信号稳定性

#### 8. 探秘指标 (探秘)

**计算公式：**
```
探秘:=EMA(0.667*REF(秘籍,1)+0.333*秘籍,2);
```

**计算逻辑：**
- 对前一日秘籍值给予66.7%权重
- 对当日秘籍值给予33.3%权重
- 进行2日EMA平滑

**技术含义：**
- 秘籍指标的平滑版本
- 更加稳定，减少假信号
- 用于趋势确认和信号过滤

### 第四阶段：选股条件

#### 9. 最终选股信号

**选股条件：**
```
CROSS(秘籍,20);
```

**触发逻辑：**
- 当秘籍指标从下方向上突破20时触发选股信号
- 表明股票从相对低位开始反弹
- 是买入的最佳时机

**技术含义：**
- 20作为买入阈值，经过大量实践验证
- 向上突破表明反弹动力开始显现
- 简洁明确的选股条件，便于执行

## 选股信号特征

### 信号质量特点
1. **时机精准**：在股票刚开始反弹时发出信号
2. **风险可控**：选择相对低位的股票，安全边际较高
3. **适应性强**：动态区间能够适应不同的市场环境
4. **执行简单**：单一条件判断，便于程序化执行

### 适用市场环境
- **震荡市场**：最适合在震荡市场中捕捉反弹机会
- **调整末期**：在市场或个股调整末期识别反转机会
- **超跌反弹**：捕捉超跌股票的技术性反弹
- **波段操作**：为波段操作提供精准的买入时机

## 使用方法

### 选股流程
1. **每日扫描**：收盘后运行选股公式
2. **信号确认**：关注秘籍指标突破20的股票
3. **技术确认**：结合其他技术指标进行确认
4. **基本面筛选**：对选出的股票进行基本面分析
5. **风险评估**：评估个股和市场风险

### 买入策略
- **信号当日买入**：在选股信号出现当日买入
- **次日开盘买入**：等待次日开盘确认后买入
- **回调买入**：等待小幅回调后买入
- **分批买入**：分2-3次建仓，降低风险

### 仓位管理
- **单股仓位**：单只股票不超过总资金的15%
- **总体仓位**：根据市场环境控制总仓位
- **风险控制**：设置5-8%的止损位
- **盈利保护**：获利15-20%后设置移动止损

## 参数优化建议

### N1参数调整
- **增大N1**：提高安全性，但可能错过一些机会
- **减小N1**：提高敏感性，但可能增加假信号
- **建议范围**：15-30之间，根据市场环境调整

### N2参数调整
- **增大N2**：降低信号频率，提高信号质量
- **减小N2**：增加信号频率，但可能降低质量
- **建议范围**：5-15之间，根据操作风格调整

## 注意事项

### 使用限制
1. **趋势市场**：在强趋势市场中效果可能减弱
2. **极端行情**：在极端行情中可能产生假信号
3. **流动性要求**：适合流动性较好的股票
4. **市场环境**：需要根据市场环境调整参数

### 风险提示
1. **技术分析局限性**：技术分析不能预测所有变化
2. **假突破风险**：部分信号可能是假突破
3. **系统性风险**：无法规避整体市场风险
4. **基本面风险**：需要关注基本面变化

### 优化建议
1. **多指标结合**：结合其他技术指标进行确认
2. **基本面结合**：将技术选股与基本面分析结合
3. **动态调整**：根据市场环境动态调整参数
4. **风险管理**：建立完善的风险管理体系

## 实战应用技巧

### 提高成功率的方法
1. **耐心等待**：等待高质量信号，不要急于操作
2. **严格执行**：严格按照信号执行，避免主观判断
3. **风险优先**：始终将风险控制放在第一位
4. **持续学习**：不断总结经验，优化使用方法

### 常见误区避免
1. **频繁交易**：避免因为信号频繁而过度交易
2. **忽视大盘**：不要忽视大盘趋势的影响
3. **重仓操作**：避免在单一信号上重仓操作
4. **情绪化操作**：保持理性，避免情绪化决策

高抛低吸买入选股公式是一个经过精心设计的选股工具，通过科学的算法和简洁的条件，为投资者提供了识别买入机会的有效方法。正确使用该公式，结合合理的风险管理和基本面分析，可以显著提高波段操作的成功率和收益水平。
