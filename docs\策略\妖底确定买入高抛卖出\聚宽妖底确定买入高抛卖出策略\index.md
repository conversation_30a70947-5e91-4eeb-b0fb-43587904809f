# 妖底确定买入高抛卖出策略 - 通用版详细解析

## 策略概述

妖底确定买入高抛卖出策略（通用版）是一个采用面向对象设计的通用量化交易策略框架。该策略通过抽象接口设计，可以适配多种数据源和交易平台，包括聚宽、AKShare、掘金等主流量化平台，为投资者提供灵活、可扩展的量化交易解决方案。

## 策略特点

### 架构优势
- **模块化设计**：采用面向对象的模块化架构
- **接口抽象**：通过抽象接口实现平台无关性
- **高度可扩展**：易于扩展和定制功能
- **代码复用**：核心逻辑可在多个平台复用
- **维护性强**：清晰的代码结构，便于维护和升级

### 技术特色
- **双指标融合**：妖底确定买入 + 高抛低吸卖出
- **智能过滤**：多重信号过滤机制
- **风险控制**：完善的风险管理体系
- **异常处理**：健壮的异常处理机制
- **日志系统**：完整的日志记录和监控

## 架构设计

### 1. 抽象接口层

#### 1.1 数据提供者接口 (DataProvider)
```python
class DataProvider(ABC):
    """数据提供者抽象基类"""
    
    @abstractmethod
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        """获取股票历史数据"""
        pass
    
    @abstractmethod
    def get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        pass
    
    @abstractmethod
    def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        pass
```

**设计理念：**
- 统一数据接口，屏蔽不同数据源的差异
- 支持多种数据源：聚宽、AKShare、掘金等
- 便于切换和扩展数据源

#### 1.2 交易提供者接口 (TradeProvider)
```python
class TradeProvider(ABC):
    """交易提供者抽象基类"""
    
    @abstractmethod
    def buy(self, symbol: str, amount: float) -> bool:
        """买入股票"""
        pass
    
    @abstractmethod
    def sell(self, symbol: str, amount: float) -> bool:
        """卖出股票"""
        pass
    
    @abstractmethod
    def get_positions(self) -> Dict[str, Dict]:
        """获取持仓信息"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Dict:
        """获取账户信息"""
        pass
```

**设计理念：**
- 统一交易接口，支持多种交易平台
- 标准化交易操作，便于策略移植
- 灵活的账户管理和持仓查询

### 2. 策略配置层

#### 2.1 策略配置类 (StrategyConfig)
```python
class StrategyConfig:
    """策略配置"""
    def __init__(self):
        self.position_ratio = 0.1      # 单次买入仓位比例
        self.trade_time = "14:55"      # 交易时间
        self.commission_rate = 0.0003  # 手续费率
        self.stamp_tax = 0.001         # 印花税
        self.min_commission = 5        # 最小手续费
```

**配置特点：**
- 集中管理策略参数
- 便于参数调整和优化
- 支持不同市场环境的配置

### 3. 策略核心层

#### 3.1 主策略类 (YaodiStrategy)
```python
class YaodiStrategy:
    """妖底确定买入高抛卖出策略"""
    
    def __init__(self, data_provider: DataProvider, 
                 trade_provider: TradeProvider, 
                 config: StrategyConfig = None):
        self.data_provider = data_provider
        self.trade_provider = trade_provider
        self.config = config or StrategyConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
```

**设计特点：**
- 依赖注入设计，提高灵活性
- 统一的日志管理
- 清晰的职责分离

## 核心功能模块

### 1. 技术指标计算

#### 1.1 SMA函数模拟
```python
@staticmethod
def sma_custom(series, period, weight=1):
    """模拟通达信SMA函数"""
    # SMA(X,N,M) = (M*X + (N-M)*Y)/N
```

**技术特点：**
- 完全兼容通达信SMA算法
- 支持加权移动平均
- 处理边界条件和异常数据

#### 1.2 FILTER函数模拟
```python
@staticmethod
def apply_filter(condition_series, period):
    """模拟通达信FILTER函数"""
    # 过滤重复信号，提高信号质量
```

**功能说明：**
- 避免指定周期内的重复信号
- 减少过度交易
- 提高信号有效性

#### 1.3 MACD计算
```python
def calculate_macd_custom(close_prices, fast=12, slow=26, signal=9):
    """自定义MACD计算"""
    # 支持talib和EMA备用算法
```

**双重保障：**
- 优先使用talib库
- EMA备用算法确保兼容性
- 自动检测和切换

### 2. 妖底确定买入算法

#### 2.1 核心指标计算
```python
def calculate_yaodi_signal(self, stock: str) -> bool:
    """计算妖底确定买入信号"""
```

**关键步骤：**
1. **基础数据获取**：获取足够的历史数据
2. **C1指标计算**：价格相对位置分析
3. **M2指标计算**：动量强度判断
4. **技术形态分析**：SMMA、TDJ等指标
5. **MACD确认**：趋势方向验证
6. **综合信号生成**：多重条件确认

#### 2.2 信号生成逻辑
```python
# 核心选股条件
选股 = FILTER(REF(启动,1) AND (确定 OR C>REF(C,1)) AND MACD.MACD>-1.5, 10)
妖底确定 = COUNT(选股,13)>=1 AND 波段
```

**多重确认机制：**
- 前一日启动条件满足
- 当日确定信号或价格上涨
- MACD不能过度负值
- 时间窗口内信号确认
- 波段过滤条件

### 3. 高抛低吸卖出算法

#### 3.1 MJ指标计算
```python
def calculate_gaopao_sell_signal(self, stock: str) -> bool:
    """计算高抛低吸卖出信号"""
```

**核心算法：**
```python
# 加权价格计算
VAR8 = (2*close + high + low) / 4

# 动态区间定义
VAR9 = close.rolling(N1).min()  # 最低价
VAR10 = high.rolling(N2).max()  # 最高价

# 相对位置计算
MJ = ((VAR8 - VAR9) / (VAR10 - VAR9) * 100).ewm(span=9).mean()

# 卖出信号
sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)
```

#### 3.2 卖出条件
- MJ指标从上方跌破80
- 表明股票从高位开始回调
- 及时止盈保护收益

### 4. 股票池管理

#### 4.1 股票筛选
```python
def get_stock_pool(self):
    """获取股票池"""
    # 排除不符合条件的股票
    # 北证、ST、停牌等
```

**筛选标准：**
- 排除北证股票
- 排除ST股票
- 排除停牌股票
- 确保流动性充足

#### 4.2 动态更新
- 每日动态更新股票池
- 自动排除不符合条件的股票
- 支持自定义筛选规则

### 5. 交易执行模块

#### 5.1 买入逻辑
```python
def check_buy_signals(self):
    """检查买入信号"""
    # 遍历股票池
    # 计算妖底确定信号
    # 执行买入操作
```

**执行流程：**
1. 获取股票池
2. 计算可用资金
3. 遍历股票池检查信号
4. 执行买入操作
5. 记录交易日志

#### 5.2 卖出逻辑
```python
def check_sell_signals(self):
    """检查卖出信号"""
    # 检查持仓股票
    # 计算卖出信号
    # 执行卖出操作
```

**执行流程：**
1. 获取当前持仓
2. 逐个检查卖出信号
3. 执行全仓卖出
4. 记录交易日志

## 平台适配示例

### 1. 聚宽平台适配
```python
class JoinQuantDataProvider(DataProvider):
    """聚宽数据提供者"""
    
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        # 使用聚宽API获取数据
        return get_price(symbol, count=count, fields=['close', 'high', 'low', 'volume'])
    
    def get_current_price(self, symbol: str) -> float:
        # 获取当前价格
        return get_current_data()[symbol].last_price
```

### 2. AKShare适配
```python
class AKShareDataProvider(DataProvider):
    """AKShare数据提供者"""
    
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        # 使用AKShare获取数据
        return ak.stock_zh_a_hist(symbol=symbol, period="daily")
```

### 3. 掘金平台适配
```python
class GoldMinerDataProvider(DataProvider):
    """掘金数据提供者"""

    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        # 使用掘金API获取数据
        return history(symbol, frequency='1d', count=count)
```

## 使用方法

### 1. 基本使用
```python
# 创建数据和交易提供者
data_provider = YourDataProvider()
trade_provider = YourTradeProvider()

# 创建策略配置
config = StrategyConfig()

# 创建策略实例
strategy = YaodiStrategy(data_provider, trade_provider, config)

# 初始化并运行
strategy.initialize()
strategy.run_daily_strategy()
```

### 2. 自定义配置
```python
# 自定义策略配置
config = StrategyConfig()
config.position_ratio = 0.15  # 调整仓位比例
config.trade_time = "14:50"   # 调整交易时间

# 使用自定义配置
strategy = YaodiStrategy(data_provider, trade_provider, config)
```

## 扩展建议

### 1. 功能扩展
- **止损机制**：添加固定止损功能
- **分批操作**：实现分批买入卖出
- **市场过滤**：添加大盘趋势过滤
- **行业轮动**：结合行业分析

### 2. 性能优化
- **数据缓存**：实现数据缓存机制
- **并行计算**：支持多线程计算
- **内存优化**：优化内存使用
- **计算加速**：使用向量化计算

### 3. 风险管理
- **最大回撤控制**：动态调整仓位
- **相关性分析**：避免过度集中
- **压力测试**：模拟极端情况
- **实时监控**：建立监控体系

## 注意事项

### 1. 使用要求
- Python 3.6+环境
- 必要的依赖库：pandas, numpy
- 可选的依赖库：talib
- 对应平台的API权限

### 2. 风险提示
- 技术分析存在局限性
- 需要充分的回测验证
- 市场环境变化可能影响效果
- 建议小资金开始测试

### 3. 维护建议
- 定期检查策略表现
- 根据市场变化调整参数
- 持续优化和改进
- 建立完善的监控体系

## 总结

妖底确定买入高抛卖出策略（通用版）是一个设计优良、功能完善的量化交易策略框架。通过模块化设计和抽象接口，实现了高度的灵活性和可扩展性，可以适配多种数据源和交易平台。

该策略结合了成熟的技术分析方法和现代软件工程实践，为量化投资者提供了一个可靠、高效的交易工具。正确使用和持续优化，可以在控制风险的前提下获得稳定的投资收益。
