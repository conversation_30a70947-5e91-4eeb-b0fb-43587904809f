{N1,0,100,21 N2,0,100,8}
VAR1:=3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
VAR2:=(CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100;
VAR3:=SMA(SMA(VAR2,3,1),3,1);
VAR4:=EMA(VAR3,5);
VAR5:=LLV(LOW,26);
VAR6:=HHV(HIGH,34);
VAR7:=EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25;
VAR8:=(2*C+H+L)/4;
VAR9:=LLV(LOW,N1);
VAR10:=HHV(HIGH,N2);
VAR2W:=100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14));
MW:= EMA(VAR2W,3);
VAR3W:=EMA(VAR2W,7);
M1:= EMA(VAR3W,5);
MB1:=CROSS(MW,M1) AND M1<20,COLORBLUE;
MG1:=IF(CROSS(M1,MW) AND REF(MW,1)>80,80 ,0);
秘籍:EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9);
探秘:EMA(0.667*REF(秘籍,1)+0.333*秘籍,2),COLORWHITE;
逃顶:IF((BARSLAST(MG1>0)<2),80,100);
见底:IF((MB1),20,0);
出击: IF((VAR7<10),50,30);
预备: IF((VAR7>90),50,70);
STICKLINE((秘籍-探秘)>0,秘籍,探秘,3,0);
STICKLINE((秘籍-探秘)<0,秘籍,探秘,3,0),COLORYELLOW;
DRAWTEXT(CROSS(秘籍,20),18,'买入'),COLORRED;
DRAWTEXT(CROSS(80,秘籍),82,'卖出'),COLORGREEN;
0;100;
