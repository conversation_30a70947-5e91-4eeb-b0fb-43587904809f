# 擒龙启爆指标系统

## 指标概述

擒龙启爆指标是一个专门用于捕捉股票底部启动信号的技术指标系统，通过多层次的技术分析算法，识别股票的起爆点和启爆器信号。该指标结合了价格相对位置、成交量变化、技术形态等多个维度的分析，为投资者提供精准的买入时机。

## 系统组成

### 指标源码
- **文件位置**: [指标源码](./指标源码/README.md)
- **Python文件**: [指标源码.py](./指标源码/指标源码.py)
- **功能说明**: 完整的擒龙启爆指标实现，包含起爆火焰、起爆点、启动点、启爆器等多个信号

### 选股公式
- **文件位置**: [起爆点选股](./起爆点选股/README.md)
- **Python文件**: [起爆点选股.py](./起爆点选股/起爆点选股.py)
- **功能说明**: 基于擒龙启爆指标的选股公式，专门筛选处于底部反转关键时刻的股票

## 主要特点

### 技术优势
- **多维度分析**：结合价格、成交量、时间等多个维度
- **层次化设计**：从基础信号到高级信号的递进式设计
- **视觉化显示**：丰富的颜色和图标设计，便于识别
- **过滤机制**：内置FILTER函数，避免重复信号干扰

### 核心信号
1. **起爆火焰信号**：识别多重均线突破的强势启动信号
2. **起爆点信号**：精确识别底部反转的关键时刻
3. **启动点信号**：确认股票已经开始启动的追涨信号
4. **启爆器信号**：量价齐升的强势爆发信号

## 使用建议

### 信号优先级
1. **启爆器信号**：最高优先级，量价齐升的强势信号
2. **起爆点信号**：高优先级，底部反转的关键信号
3. **起爆火焰信号**：中等优先级，多重突破确认信号
4. **启动点信号**：辅助信号，用于追涨确认

### 组合使用策略
- **最佳买入时机**：起爆点信号出现后，等待启爆器信号确认
- **追涨策略**：启动点信号出现时可以考虑追涨
- **趋势确认**：结合趋势指示线判断整体方向

## 风险提示

1. **时效性**：指标设置了有效期，超过期限可能影响准确性
2. **市场环境**：在极端市场环境下，信号可能出现偏差
3. **成交量确认**：重要信号需要成交量配合确认
4. **假突破风险**：短期突破信号需要后续确认，避免假突破
5. **综合分析**：建议结合其他技术指标和基本面分析使用

## 适用性

- **适用市场**：A股市场，特别适合震荡市和反转行情
- **适用周期**：日线级别，适合中短期投资
- **适用对象**：流动性好、基本面健康的股票
- **投资风格**：适合波段操作和趋势跟踪

该指标系统通过精密的算法设计和严格的过滤机制，为投资者提供了一个可靠的底部选股和时机把握工具。正确使用该指标，结合合理的风险管理，可以显著提高投资成功率。
