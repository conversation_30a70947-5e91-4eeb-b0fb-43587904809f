# 文档索引

## 概述

本文档提供了docs目录下所有Markdown文件的完整索引，以及它们在VitePress中的对应链接。

## 文档结构

### 📊 指标文档

#### 指标总览
- **文件路径**: `docs/指标/index.md`
- **VitePress链接**: `/docs/指标/`
- **导航位置**: 指标总览 > 指标概述

#### 擒龙启爆指标系统
- **系统概述**
  - **文件路径**: `docs/指标/擒龙启爆/index.md`
  - **VitePress链接**: `/docs/指标/擒龙启爆/`
  - **导航位置**: 指标详解 > 擒龙启爆指标系统

- **指标源码解析**
  - **文件路径**: `docs/指标/擒龙启爆/指标源码/README.md`
  - **VitePress链接**: `/docs/指标/擒龙启爆/指标源码/README`
  - **导航位置**: 擒龙启爆指标 > 指标源码解析
  - **Python文件**: `docs/指标/擒龙启爆/指标源码/指标源码.py`

- **起爆点选股解析**
  - **文件路径**: `docs/指标/擒龙启爆/起爆点选股/README.md`
  - **VitePress链接**: `/docs/指标/擒龙启爆/起爆点选股/README`
  - **导航位置**: 擒龙启爆指标 > 起爆点选股解析
  - **Python文件**: `docs/指标/擒龙启爆/起爆点选股/起爆点选股.py`

#### 金牛暴起指标系统
- **系统概述**
  - **文件路径**: `docs/指标/金牛暴起/index.md`
  - **VitePress链接**: `/docs/指标/金牛暴起/`
  - **导航位置**: 指标详解 > 金牛暴起指标系统

- **指标源码解析**
  - **文件路径**: `docs/指标/金牛暴起/指标源码/README.md`
  - **VitePress链接**: `/docs/指标/金牛暴起/指标源码/README`
  - **导航位置**: 金牛暴起指标 > 指标源码解析
  - **Python文件**: `docs/指标/金牛暴起/指标源码/指标源码.py`

- **妖底确定选股解析**
  - **文件路径**: `docs/指标/金牛暴起/妖底确定选股/README.md`
  - **VitePress链接**: `/docs/指标/金牛暴起/妖底确定选股/README`
  - **导航位置**: 金牛暴起指标 > 妖底确定选股解析
  - **Python文件**: `docs/指标/金牛暴起/妖底确定选股/妖底确定选股.py`

#### 高抛低吸指标系统
- **系统概述**
  - **文件路径**: `docs/指标/高抛低吸/index.md`
  - **VitePress链接**: `/docs/指标/高抛低吸/`
  - **导航位置**: 指标详解 > 高抛低吸指标系统

- **指标源码解析**
  - **文件路径**: `docs/指标/高抛低吸/指标源码/README.md`
  - **VitePress链接**: `/docs/指标/高抛低吸/指标源码/README`
  - **导航位置**: 高抛低吸指标 > 指标源码解析
  - **Python文件**: `docs/指标/高抛低吸/指标源码/指标源码.py`

- **买入选股解析**
  - **文件路径**: `docs/指标/高抛低吸/买入选股/README.md`
  - **VitePress链接**: `/docs/指标/高抛低吸/买入选股/README`
  - **导航位置**: 高抛低吸指标 > 买入选股解析
  - **Python文件**: `docs/指标/高抛低吸/买入选股/买入选股.py`

- **卖出选股解析**
  - **文件路径**: `docs/指标/高抛低吸/卖出选股/README.md`
  - **VitePress链接**: `/docs/指标/高抛低吸/卖出选股/README`
  - **导航位置**: 高抛低吸指标 > 卖出选股解析
  - **Python文件**: `docs/指标/高抛低吸/卖出选股/卖出选股.py`

### 🎯 策略文档

#### 策略总览
- **文件路径**: `docs/策略/index.md`
- **VitePress链接**: `/docs/策略/`
- **导航位置**: 策略总览 > 策略概述

#### 妖底确定买入高抛卖出策略系统
- **系统概述**
  - **文件路径**: `docs/策略/妖底确定买入高抛卖出/index.md`
  - **VitePress链接**: `/docs/策略/妖底确定买入高抛卖出/`
  - **导航位置**: 妖底确定买入高抛卖出 > 策略系统概述

- **掘金版策略解析**
  - **文件路径**: `docs/策略/妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版/README.md`
  - **VitePress链接**: `/docs/策略/妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版/README`
  - **导航位置**: 策略源码解析 > 掘金版策略解析
  - **Python文件**: `docs/策略/妖底确定买入高抛卖出/妖底确定买入高抛卖出策略_掘金版/妖底确定买入高抛卖出策略_掘金版.py`

- **通用版策略解析**
  - **文件路径**: `docs/策略/妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略/README.md`
  - **VitePress链接**: `/docs/策略/妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略/README`
  - **导航位置**: 策略源码解析 > 通用版策略解析
  - **Python文件**: `docs/策略/妖底确定买入高抛卖出/聚宽妖底确定买入高抛卖出策略/聚宽妖底确定买入高抛卖出策略.py`

### 📋 工作流文档

#### 文档生成工作流
- **文件路径**: `workflow/文档生成工作流.md`
- **VitePress链接**: `/workflow/文档生成工作流`
- **导航位置**: 工作流文档 > 文档生成工作流

## 文档统计

### 📊 数量统计
- **总Markdown文件**: 13个
- **指标相关文档**: 10个（包含1个总览 + 3个系统概述 + 6个详细解析）
- **策略相关文档**: 3个（包含1个总览 + 1个系统概述 + 2个详细解析）
- **工作流文档**: 1个
- **Python源码文件**: 9个

### 📁 文件夹结构
- **指标系统**: 3个（擒龙启爆、金牛暴起、高抛低吸）
- **策略系统**: 1个（妖底确定买入高抛卖出）
- **每个指标**: 2-3个子模块
- **每个策略**: 2个版本（掘金版、通用版）

### 🔗 导航结构
- **主导航**: 4个（首页、指标、策略、工作流）
- **指标侧边栏**: 4个分组，10个链接
- **策略侧边栏**: 2个分组，3个链接
- **工作流侧边栏**: 1个分组，1个链接

## VitePress配置完整性

### ✅ 已配置项目
1. **主导航栏**: 所有主要部分都有导航入口
2. **侧边栏**: 每个部分都有完整的侧边栏配置
3. **文件链接**: 所有README.md文件都有对应的VitePress链接
4. **系统概述**: 每个主要系统都有index.md概述页面
5. **死链接忽略**: 配置了Python文件的死链接忽略

### 🎯 导航逻辑
1. **层次清晰**: 总览 → 系统概述 → 详细解析
2. **分类明确**: 指标和策略分别组织
3. **访问便捷**: 多个入口可以访问同一内容
4. **结构完整**: 每个文件都有明确的导航路径

## 使用建议

### 📖 阅读路径
1. **新手用户**: 从总览页面开始，了解整体结构
2. **技术用户**: 直接访问具体的指标或策略解析
3. **开发者**: 参考工作流文档了解文档生成过程

### 🔍 搜索功能
- VitePress配置了本地搜索功能
- 可以快速搜索所有文档内容
- 支持中文搜索

### 📱 响应式设计
- 所有页面都支持移动端访问
- 侧边栏在移动端自动折叠
- 导航结构适配不同屏幕尺寸

---

**文档索引版本**: v1.0  
**创建日期**: 2025-01-04  
**包含文件**: 13个Markdown文件 + 9个Python文件  
**VitePress版本**: 最新稳定版
