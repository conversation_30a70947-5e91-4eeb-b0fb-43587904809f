# 妖底确定买入高抛卖出策略 - 掘金版详细解析

## 策略概述

妖底确定买入高抛卖出策略（掘金版）是一个专门为掘金量化平台设计的完整量化交易策略。该策略结合了妖底确定选股指标和高抛低吸卖出指标，通过精密的技术分析算法，实现自动化的股票买卖操作，旨在捕捉股票底部反转机会并在高位及时止盈。

## 策略特点

### 核心优势
- **双指标融合**：结合妖底确定和高抛低吸两大核心指标
- **自动化执行**：完全自动化的买卖决策和执行
- **风险可控**：内置多重风险控制机制
- **平台适配**：专门为掘金平台优化设计
- **实盘验证**：经过实盘交易验证的成熟策略

### 技术特色
- **精准时机**：每日收盘前5分钟执行，避免盘中噪音
- **仓位管理**：十分之一仓位买入，支持加仓操作
- **信号过滤**：多重过滤机制，减少假信号干扰
- **容错设计**：内置异常处理和备用算法

## 策略逻辑

### 核心交易逻辑
1. **买入逻辑**：使用妖底确定选股公式筛选买入标的
2. **卖出逻辑**：使用高抛低吸卖出公式判断卖出时机
3. **时间控制**：每日14:55执行策略，避免收盘前波动
4. **仓位管理**：单次买入十分之一仓位，可多次加仓

### 执行流程
```
策略初始化 → 定时任务设置 → 每日14:55执行 → 检查卖出信号 → 检查买入信号 → 执行交易 → 记录日志
```

## 详细功能模块

### 1. 策略初始化 (init函数)

**核心配置：**
```python
context.position_ratio = 0.1      # 单次买入仓位比例
context.trade_time = "14:55"      # 交易时间
context.commission_rate = 0.0003  # 手续费率
context.stamp_tax = 0.001         # 印花税
context.min_commission = 5        # 最小手续费
```

**定时任务设置：**
```python
schedule(schedule_func=trade_strategy, date_rule='1d', time_rule='14:55:00')
```

**功能说明：**
- 设置策略运行的基本参数
- 配置交易成本相关参数
- 建立每日定时执行机制
- 初始化日志记录系统

### 2. 主交易策略 (trade_strategy函数)

**执行顺序：**
1. **卖出检查**：优先检查持仓股票的卖出信号
2. **买入检查**：在卖出完成后检查新的买入机会
3. **状态记录**：记录策略执行结果和持仓状态

**设计理念：**
- 卖出优先原则，及时止盈控制风险
- 买入谨慎原则，严格按照信号执行
- 完整记录原则，便于策略分析和优化

### 3. 技术指标计算模块

#### 3.1 自定义SMA函数
```python
def sma_custom(series, period, weight=1):
    """模拟通达信的SMA函数"""
    # SMA(X,N,M) = (M*X + (N-M)*Y)/N
```

**技术特点：**
- 完全模拟通达信SMA算法
- 支持加权移动平均计算
- 处理数据缺失和异常情况

#### 3.2 FILTER函数模拟
```python
def apply_filter(condition_series, period):
    """模拟通达信FILTER函数"""
    # 避免重复信号，提高信号质量
```

**功能说明：**
- 过滤指定周期内的重复信号
- 避免频繁交易，降低交易成本
- 提高信号的有效性和稳定性

#### 3.3 MACD计算
```python
def calculate_macd(close_prices, fast=12, slow=26, signal=9):
    """计算MACD指标，支持talib和备用算法"""
```

**双重保障：**
- 优先使用talib库计算，精度更高
- 备用EMA算法，确保策略稳定运行
- 自动检测和切换计算方法

### 4. 妖底确定买入信号

#### 4.1 核心算法实现
```python
def calculate_yaodi_signal(stock):
    """计算妖底确定买入信号"""
    # 多重技术指标综合判断
    # 严格按照选股公式实现
```

**关键指标：**
- **C1指标**：价格相对位置分析
- **M2指标**：动量强度判断
- **G1条件**：突破过滤机制
- **TU条件**：价格压制确认
- **SMMA系统**：趋势平滑分析
- **TDJ条件**：振幅活跃度
- **MACD确认**：趋势方向验证

#### 4.2 信号生成逻辑
```python
# 最终选股条件
选股 = FILTER(REF(启动,1) AND (确定 OR C>REF(C,1)) AND MACD.MACD>-1.5, 10)
妖底确定 = COUNT(选股,13)>=1 AND 波段
```

**多重确认机制：**
- 前一日满足启动条件
- 当日确定信号或价格上涨
- MACD指标不能过度负值
- 13日内至少出现1次选股信号
- 同时满足波段过滤条件

### 5. 高抛低吸卖出信号

#### 5.1 MJ指标计算
```python
def calculate_gaopao_sell_signal(stock):
    """计算高抛低吸卖出信号"""
    # 基于MJ指标的卖出判断
```

**核心算法：**
```python
VAR8 = (2*C + H + L) / 4  # 加权价格
VAR9 = LLV(LOW, N1)       # 区间下限
VAR10 = HHV(HIGH, N2)     # 区间上限
MJ = EMA((VAR8-VAR9)/(VAR10-VAR9)*100, 9)  # 相对位置
```

#### 5.2 卖出条件
```python
# 卖出信号：MJ指标从上方跌破80
sell_signal = CROSS(80, MJ)
```

**信号特点：**
- 80作为卖出阈值，经过实践验证
- 向下跌破表明上升动力衰竭
- 及时止盈，保护收益

### 6. 股票池管理

#### 6.1 股票筛选标准
```python
def get_stock_pool(context):
    """获取股票池，排除不符合条件的股票"""
```

**筛选条件：**
- 排除北证股票（代码以4或8开头）
- 排除ST股票（名称包含ST）
- 排除停牌股票
- 确保股票流动性充足

#### 6.2 动态股票池
- 每日动态更新股票池
- 自动排除不符合条件的股票
- 支持自定义筛选条件

### 7. 风险控制机制

#### 7.1 仓位控制
- **单次买入**：不超过总资金的10%
- **总仓位**：通过买入次数控制总仓位
- **加仓机制**：已持有股票可以继续加仓
- **全仓卖出**：卖出信号时全仓卖出

#### 7.2 交易控制
- **时间控制**：固定在收盘前5分钟执行
- **信号过滤**：多重过滤机制避免假信号
- **异常处理**：完善的异常处理机制
- **日志记录**：详细的交易日志记录

## 使用方法

### 1. 环境配置
```python
# 掘金平台环境
# Python 3.6+
# pandas, numpy
# talib (可选)
```

### 2. 策略部署
1. 将策略代码复制到掘金平台
2. 设置策略参数（如需要）
3. 选择回测或实盘模式
4. 启动策略运行

### 3. 参数调整
```python
# 可调整参数
context.position_ratio = 0.1    # 仓位比例
context.trade_time = "14:55"    # 交易时间
# 其他成本参数根据实际情况调整
```

## 策略优化建议

### 1. 参数优化
- **仓位比例**：根据风险偏好调整
- **交易时间**：根据市场特点调整
- **过滤周期**：根据信号频率调整

### 2. 功能扩展
- **止损机制**：添加固定止损功能
- **分批卖出**：实现分批减仓功能
- **市场过滤**：添加大盘趋势过滤
- **行业轮动**：结合行业轮动策略

### 3. 风险管理
- **最大回撤控制**：设置最大回撤限制
- **单股仓位限制**：限制单只股票最大仓位
- **总仓位控制**：根据市场环境调整总仓位

## 注意事项

### 1. 使用限制
- 适用于A股市场
- 需要掘金平台环境
- 建议在模拟环境充分测试后再实盘使用

### 2. 风险提示
- 技术分析存在局限性
- 市场极端情况下可能失效
- 需要持续监控和优化

### 3. 维护建议
- 定期检查策略运行状态
- 根据市场变化调整参数
- 持续优化和改进策略

## 实盘应用经验

### 1. 最佳实践
- 充分回测验证策略有效性
- 小资金开始，逐步增加投入
- 严格按照策略信号执行
- 定期分析策略表现

### 2. 常见问题
- **信号延迟**：确保数据及时更新
- **执行偏差**：注意交易时间和流动性
- **参数失效**：根据市场变化调整参数

### 3. 优化方向
- 结合基本面分析
- 增加市场情绪指标
- 优化买卖时机选择
- 完善风险控制体系

该策略是一个经过实践验证的量化交易策略，通过科学的技术分析和严格的风险控制，为投资者提供了一个可靠的自动化交易解决方案。正确使用和持续优化，可以在控制风险的前提下获得稳定的投资收益。
