# GitHub Pages 部署说明

## 概述

本项目已配置GitHub Actions工作流，可以自动构建VitePress文档并部署到GitHub Pages。

## 工作流文件

创建了三个工作流文件：

1. **`.github/workflows/deploy-simple.yml`** - 简化版工作流 ✅ **推荐使用**
   - 配置简单，稳定可靠
   - 适合初次使用和日常部署

2. **`.github/workflows/deploy-stable.yml`** - 稳定版工作流
   - 包含pnpm缓存优化
   - 使用corepack管理pnpm版本
   - 适合需要缓存优化的场景

3. **`.github/workflows/deploy.yml`** - 完整版工作流
   - 最完整的配置选项
   - 可能需要调试配置问题

## 工作流选择建议

- **首次使用**：建议使用 `deploy-simple.yml`
- **需要缓存优化**：使用 `deploy-stable.yml`
- **高级用户**：可以尝试 `deploy.yml` 并根据需要调整

**注意**：同时只能启用一个工作流，建议禁用其他工作流文件（重命名为 `.yml.disabled`）

## GitHub Pages 配置步骤

### 1. 启用GitHub Pages

1. 进入您的GitHub仓库
2. 点击 **Settings** 标签页
3. 在左侧菜单中找到 **Pages**
4. 在 **Source** 部分选择 **GitHub Actions**

### 2. 配置权限

确保工作流有足够的权限：

1. 在仓库设置中，进入 **Actions** > **General**
2. 在 **Workflow permissions** 部分：
   - 选择 **Read and write permissions**
   - 勾选 **Allow GitHub Actions to create and approve pull requests**

### 3. 触发部署

工作流会在以下情况自动触发：

- 推送代码到 `main` 或 `master` 分支
- 手动触发（在Actions页面点击"Run workflow"）

### 4. 查看部署状态

1. 进入仓库的 **Actions** 标签页
2. 查看工作流运行状态
3. 部署成功后，可以通过以下地址访问文档：

   ```url
   https://[用户名].github.io/[仓库名]/
   ```

   **注意**：已在VitePress配置中设置了正确的base路径 `/stock/`

## 本地开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 构建生产版本
pnpm run build

# 预览构建结果
pnpm run preview
```

## 故障排除

### 常见问题

1. **部署失败 - 权限错误**
   - 检查仓库的Actions权限设置
   - 确保已启用GitHub Pages

2. **构建失败 - 依赖问题**
   - 检查`package.json`中的依赖版本
   - 尝试删除`node_modules`和`pnpm-lock.yaml`后重新安装

3. **构建失败 - 死链接错误**
   - VitePress会检查文档中的链接是否有效
   - 已在配置中设置忽略Python文件和特定文件的链接检查
   - 如需修改，编辑`.vitepress/config.mjs`中的`ignoreDeadLinks`配置

4. **页面404错误或样式丢失**
   - 检查VitePress配置中的base路径（应为 `/仓库名/`）
   - 确认GitHub Pages的源设置正确
   - 如果仓库名是 `stock`，base应设置为 `/stock/`

### 调试步骤

1. 查看Actions日志中的详细错误信息
2. 在本地运行`pnpm run build`测试构建
3. 检查`.vitepress/dist`目录是否正确生成

## 自定义配置

如需修改部署配置，可以编辑：

- `.github/workflows/deploy.yml` - 主要工作流配置
- `.vitepress/config.mjs` - VitePress配置
- `package.json` - 构建脚本配置

## 注意事项

- 确保所有Markdown文件使用UTF-8编码
- 图片和静态资源应放在`public`目录或相对路径引用
- 避免在文件名中使用特殊字符
